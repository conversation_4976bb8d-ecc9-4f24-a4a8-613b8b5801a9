import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  TextInput,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '@/store/AuthProvider';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { CompositeScreenProps } from '@react-navigation/native';
import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { MainTabParamList, MainStackParamList } from '@/navigation/MainNavigator';

type ExpensesScreenProps = CompositeScreenProps<
  BottomTabScreenProps<MainTabParamList, 'Finances'>,
  NativeStackScreenProps<MainStackParamList>
>;

type Expense = {
  id: string;
  category: string;
  amount: number;
  date: string;
  description?: string;
  vendor?: string;
  receiptUrl?: string;
};

const ExpensesScreen: React.FC<ExpensesScreenProps> = ({ navigation }) => {
  const { user } = useAuth();
  const [expenses, setExpenses] = useState<Expense[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredExpenses, setFilteredExpenses] = useState<Expense[]>([]);

  useEffect(() => {
    // This would normally be an API call
    // For now, we'll use mock data
    const mockExpenses: Expense[] = [
      { 
        id: '1', 
        category: 'Supplies', 
        amount: 1200, 
        date: '2023-09-12', 
        description: 'Fertilizer purchase', 
        vendor: 'AgriSupply Co.',
        receiptUrl: 'https://example.com/receipts/1.pdf'
      },
      { 
        id: '2', 
        category: 'Fuel', 
        amount: 850, 
        date: '2023-09-08', 
        description: 'Diesel for tractors', 
        vendor: 'Shell Gas Station',
      },
      { 
        id: '3', 
        category: 'Maintenance', 
        amount: 2500, 
        date: '2023-09-05', 
        description: 'Equipment repair - John Deere tractor', 
        vendor: 'John Deere Service Center',
        receiptUrl: 'https://example.com/receipts/3.pdf'
      },
      { 
        id: '4', 
        category: 'Seeds', 
        amount: 3500, 
        date: '2023-08-28', 
        description: 'Corn seeds for next season', 
        vendor: 'Pioneer Seeds',
        receiptUrl: 'https://example.com/receipts/4.pdf'
      },
      { 
        id: '5', 
        category: 'Labor', 
        amount: 4200, 
        date: '2023-08-25', 
        description: 'Seasonal workers - August', 
        vendor: 'Farm Labor LLC',
      },
    ];

    setExpenses(mockExpenses);
    setFilteredExpenses(mockExpenses);
    setLoading(false);
  }, []);

  useEffect(() => {
    if (searchQuery) {
      const filtered = expenses.filter(expense => 
        expense.category.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (expense.description && expense.description.toLowerCase().includes(searchQuery.toLowerCase())) ||
        (expense.vendor && expense.vendor.toLowerCase().includes(searchQuery.toLowerCase())) ||
        expense.date.includes(searchQuery)
      );
      setFilteredExpenses(filtered);
    } else {
      setFilteredExpenses(expenses);
    }
  }, [searchQuery, expenses]);

  const handleAddExpense = () => {
    // In a real app, we would navigate to an expense creation screen
    Alert.alert(
      'Add New Expense',
      'This functionality would allow you to add a new expense record.'
    );
  };

  const formatCurrency = (amount: number) => {
    return `$${amount.toLocaleString()}`;
  };

  const getCategoryIcon = (category: string) => {
    switch (category.toLowerCase()) {
      case 'supplies':
        return 'cart-outline';
      case 'fuel':
        return 'flame-outline';
      case 'maintenance':
        return 'construct-outline';
      case 'seeds':
        return 'leaf-outline';
      case 'labor':
        return 'people-outline';
      case 'utilities':
        return 'flash-outline';
      case 'rent':
        return 'home-outline';
      case 'insurance':
        return 'shield-outline';
      case 'taxes':
        return 'document-text-outline';
      default:
        return 'cash-outline';
    }
  };

  const renderExpenseItem = ({ item }: { item: Expense }) => (
    <TouchableOpacity
      style={styles.expenseItem}
      onPress={() => navigation.navigate('ExpenseDetail', { expenseId: item.id })}
    >
      <View style={styles.expenseIcon}>
        <Ionicons 
          name={getCategoryIcon(item.category)} 
          size={24} 
          color="#ef4444" 
        />
      </View>
      <View style={styles.expenseInfo}>
        <Text style={styles.expenseDescription}>
          {item.description || `${item.category} expense`}
        </Text>
        <Text style={styles.expenseCategory}>{item.category}</Text>
        <View style={styles.expenseDetails}>
          <Text style={styles.expenseDate}>{item.date}</Text>
          {item.vendor && (
            <Text style={styles.expenseVendor}>• {item.vendor}</Text>
          )}
        </View>
      </View>
      <Text style={styles.expenseAmount}>{formatCurrency(item.amount)}</Text>
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#22c55e" />
        <Text style={styles.loadingText}>Loading expenses...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.searchContainer}>
        <Ionicons name="search" size={20} color="#999" style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search expenses..."
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
        {searchQuery ? (
          <TouchableOpacity onPress={() => setSearchQuery('')}>
            <Ionicons name="close-circle" size={20} color="#999" />
          </TouchableOpacity>
        ) : null}
      </View>

      {filteredExpenses.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Ionicons name="cash" size={60} color="#d1d5db" />
          <Text style={styles.emptyText}>No expenses found</Text>
          <Text style={styles.emptySubtext}>
            {searchQuery 
              ? "Try a different search term" 
              : "Add your first expense to get started"}
          </Text>
        </View>
      ) : (
        <FlatList
          data={filteredExpenses}
          renderItem={renderExpenseItem}
          keyExtractor={item => item.id}
          contentContainerStyle={styles.listContent}
        />
      )}

      <TouchableOpacity style={styles.addButton} onPress={handleAddExpense}>
        <Ionicons name="add" size={24} color="#fff" />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    paddingHorizontal: 15,
    paddingVertical: 10,
    marginHorizontal: 15,
    marginTop: 15,
    marginBottom: 10,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    height: 40,
    fontSize: 16,
  },
  listContent: {
    paddingHorizontal: 15,
    paddingBottom: 80, // Space for the add button
  },
  expenseItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    padding: 15,
    borderRadius: 8,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  expenseIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(239, 68, 68, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  expenseInfo: {
    flex: 1,
  },
  expenseDescription: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
  },
  expenseCategory: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  expenseDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 2,
  },
  expenseDate: {
    fontSize: 12,
    color: '#999',
  },
  expenseVendor: {
    fontSize: 12,
    color: '#999',
    marginLeft: 5,
  },
  expenseAmount: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#ef4444',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '500',
    color: '#666',
    marginTop: 10,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#999',
    marginTop: 5,
    textAlign: 'center',
  },
  addButton: {
    position: 'absolute',
    right: 20,
    bottom: 20,
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#ef4444',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 5,
  },
});

export default ExpensesScreen;