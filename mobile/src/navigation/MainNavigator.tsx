import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { Ionicons } from '@expo/vector-icons';
import HomeScreen from '@/screens/main/HomeScreen';
import MapScreen from '@/screens/main/MapScreen';
import FieldsScreen from '@/screens/main/FieldsScreen';
import TasksScreen from '@/screens/main/TasksScreen';
import ProfileScreen from '@/screens/main/ProfileScreen';
import EquipmentScreen from '@/screens/main/EquipmentScreen';
import InventoryScreen from '@/screens/main/InventoryScreen';
import FieldDetailScreen from '@/screens/main/FieldDetailScreen';
import TaskDetailScreen from '@/screens/main/TaskDetailScreen';
import EquipmentDetailScreen from '@/screens/main/EquipmentDetailScreen';
import EquipmentFormScreen from '@/screens/main/EquipmentFormScreen';
import MaintenanceFormScreen from '@/screens/main/MaintenanceFormScreen';
import InventoryDetailScreen from '@/screens/main/InventoryDetailScreen';
import RecordingScreen from '@/screens/main/RecordingScreen';
import EmployeesScreen from '@/screens/main/EmployeesScreen';
import EmployeeDetailScreen from '@/screens/main/EmployeeDetailScreen';
import TimeEntryScreen from '@/screens/main/TimeEntryScreen';
import FinancesScreen from '@/screens/main/FinancesScreen';
import ExpensesScreen from '@/screens/main/ExpensesScreen';
import ExpenseDetailScreen from '@/screens/main/ExpenseDetailScreen';
import InvoicesScreen from '@/screens/main/InvoicesScreen';
import InvoiceDetailScreen from '@/screens/main/InvoiceDetailScreen';
import CustomersScreen from '@/screens/main/CustomersScreen';
import CustomerDetailScreen from '@/screens/main/CustomerDetailScreen';
import CropScoutingScreen from '@/screens/main/CropScoutingScreen';
import LoRaWANMessaging from '@/screens/LoRaWANMessaging';
import OfflineFieldMappingScreen from '@/screens/main/OfflineFieldMappingScreen';
import VoiceCommandsScreen from '@/screens/main/VoiceCommandsScreen';
import AugmentedRealityScreen from '@/screens/main/AugmentedRealityScreen';
import EquipmentDiagnosticsScreen from '@/screens/main/EquipmentDiagnosticsScreen';

export type MainStackParamList = {
  MainTabs: undefined;
  FieldDetail: { fieldId: string };
  TaskDetail: { taskId: string };
  EquipmentDetail: { equipmentId: string };
  EquipmentForm: { equipmentId?: string };
  MaintenanceForm: { equipmentId: string; recordId?: string };
  InventoryDetail: { inventoryId: string };
  EmployeeDetail: { employeeId: string };
  TimeEntry: { employeeId?: string; taskId?: string; timeEntryId?: string };
  ExpenseDetail: { expenseId: string };
  InvoiceDetail: { invoiceId: string };
  CustomerDetail: { customerId: string };
  Recording: undefined;
  CropScouting: { fieldId: string };
  LoRaWANMessaging: undefined;
  OfflineFieldMapping: undefined;
  VoiceCommands: undefined;
  AugmentedReality: undefined;
  EquipmentDiagnostics: undefined;
};

export type MainTabParamList = {
  Home: undefined;
  Fields: undefined;
  Map: undefined;
  Tasks: undefined;
  Equipment: undefined;
  Inventory: undefined;
  Employees: undefined;
  Finances: undefined;
  Profile: undefined;
};

const Stack = createNativeStackNavigator<MainStackParamList>();
const Tab = createBottomTabNavigator<MainTabParamList>();

const MainTabs: React.FC = () => {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: keyof typeof Ionicons.glyphMap = 'home';

          if (route.name === 'Home') {
            iconName = focused ? 'home' : 'home-outline';
          } else if (route.name === 'Fields') {
            iconName = focused ? 'grid' : 'grid-outline';
          } else if (route.name === 'Map') {
            iconName = focused ? 'map' : 'map-outline';
          } else if (route.name === 'Tasks') {
            iconName = focused ? 'list' : 'list-outline';
          } else if (route.name === 'Equipment') {
            iconName = focused ? 'construct' : 'construct-outline';
          } else if (route.name === 'Inventory') {
            iconName = focused ? 'cube' : 'cube-outline';
          } else if (route.name === 'Employees') {
            iconName = focused ? 'people' : 'people-outline';
          } else if (route.name === 'Finances') {
            iconName = focused ? 'wallet' : 'wallet-outline';
          } else if (route.name === 'Profile') {
            iconName = focused ? 'person' : 'person-outline';
          }

          return <Ionicons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: '#22c55e',
        tabBarInactiveTintColor: 'gray',
        headerShown: true,
        headerStyle: {
          backgroundColor: '#22c55e',
        },
        headerTintColor: '#fff',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      })}
    >
      <Tab.Screen name="Home" component={HomeScreen} options={{ title: 'Dashboard' }} />
      <Tab.Screen name="Fields" component={FieldsScreen} options={{ title: 'Fields' }} />
      <Tab.Screen name="Map" component={MapScreen} options={{ title: 'Map' }} />
      <Tab.Screen name="Tasks" component={TasksScreen} options={{ title: 'Tasks' }} />
      <Tab.Screen name="Equipment" component={EquipmentScreen} options={{ title: 'Equipment' }} />
      <Tab.Screen name="Inventory" component={InventoryScreen} options={{ title: 'Inventory' }} />
      <Tab.Screen name="Employees" component={EmployeesScreen} options={{ title: 'Employees' }} />
      <Tab.Screen name="Finances" component={FinancesScreen} options={{ title: 'Finances' }} />
      <Tab.Screen name="Profile" component={ProfileScreen} options={{ title: 'Profile' }} />
    </Tab.Navigator>
  );
};

const MainNavigator: React.FC = () => {
  return (
    <Stack.Navigator
      initialRouteName="MainTabs"
      screenOptions={{
        headerShown: false,
      }}
    >
      <Stack.Screen name="MainTabs" component={MainTabs} />
      <Stack.Screen 
        name="FieldDetail" 
        component={FieldDetailScreen} 
        options={{ 
          headerShown: true,
          headerStyle: { backgroundColor: '#22c55e' },
          headerTintColor: '#fff',
          title: 'Field Details'
        }} 
      />
      <Stack.Screen 
        name="TaskDetail" 
        component={TaskDetailScreen} 
        options={{ 
          headerShown: true,
          headerStyle: { backgroundColor: '#22c55e' },
          headerTintColor: '#fff',
          title: 'Task Details'
        }} 
      />
      <Stack.Screen 
        name="Recording" 
        component={RecordingScreen} 
        options={{ 
          headerShown: true,
          headerStyle: { backgroundColor: '#22c55e' },
          headerTintColor: '#fff',
          title: 'GPS Recording'
        }} 
      />
      <Stack.Screen 
        name="EquipmentDetail" 
        component={EquipmentDetailScreen} 
        options={{ 
          headerShown: true,
          headerStyle: { backgroundColor: '#22c55e' },
          headerTintColor: '#fff',
          title: 'Equipment Details'
        }} 
      />
      <Stack.Screen 
        name="EquipmentForm" 
        component={EquipmentFormScreen} 
        options={{ 
          headerShown: true,
          headerStyle: { backgroundColor: '#22c55e' },
          headerTintColor: '#fff',
          title: ({ route }) => route.params?.equipmentId ? 'Edit Equipment' : 'Add Equipment'
        }} 
      />
      <Stack.Screen 
        name="MaintenanceForm" 
        component={MaintenanceFormScreen} 
        options={{ 
          headerShown: true,
          headerStyle: { backgroundColor: '#22c55e' },
          headerTintColor: '#fff',
          title: ({ route }) => route.params?.recordId ? 'Edit Maintenance Record' : 'Add Maintenance Record'
        }} 
      />
      <Stack.Screen 
        name="InventoryDetail" 
        component={InventoryDetailScreen} 
        options={{ 
          headerShown: true,
          headerStyle: { backgroundColor: '#22c55e' },
          headerTintColor: '#fff',
          title: 'Inventory Details'
        }} 
      />
      <Stack.Screen 
        name="EmployeeDetail" 
        component={EmployeeDetailScreen} 
        options={{ 
          headerShown: true,
          headerStyle: { backgroundColor: '#22c55e' },
          headerTintColor: '#fff',
          title: 'Employee Details'
        }} 
      />
      <Stack.Screen 
        name="TimeEntry" 
        component={TimeEntryScreen} 
        options={{ 
          headerShown: true,
          headerStyle: { backgroundColor: '#22c55e' },
          headerTintColor: '#fff',
          title: 'Time Entry'
        }} 
      />
      <Stack.Screen 
        name="ExpenseDetail" 
        component={ExpenseDetailScreen} 
        options={{ 
          headerShown: true,
          headerStyle: { backgroundColor: '#22c55e' },
          headerTintColor: '#fff',
          title: 'Expense Details'
        }} 
      />
      <Stack.Screen 
        name="InvoiceDetail" 
        component={InvoiceDetailScreen} 
        options={{ 
          headerShown: true,
          headerStyle: { backgroundColor: '#22c55e' },
          headerTintColor: '#fff',
          title: 'Invoice Details'
        }} 
      />
      <Stack.Screen 
        name="CustomerDetail" 
        component={CustomerDetailScreen} 
        options={{ 
          headerShown: true,
          headerStyle: { backgroundColor: '#22c55e' },
          headerTintColor: '#fff',
          title: 'Customer Details'
        }} 
      />
      <Stack.Screen 
        name="CropScouting" 
        component={CropScoutingScreen} 
        options={{ 
          headerShown: true,
          headerStyle: { backgroundColor: '#22c55e' },
          headerTintColor: '#fff',
          title: 'Crop Scouting'
        }} 
      />
      <Stack.Screen 
        name="LoRaWANMessaging" 
        component={LoRaWANMessaging} 
        options={{ 
          headerShown: true,
          headerStyle: { backgroundColor: '#22c55e' },
          headerTintColor: '#fff',
          title: 'LoRaWAN Messaging'
        }} 
      />
      <Stack.Screen 
        name="OfflineFieldMapping" 
        component={OfflineFieldMappingScreen} 
        options={{ 
          headerShown: true,
          headerStyle: { backgroundColor: '#22c55e' },
          headerTintColor: '#fff',
          title: 'Offline Field Mapping'
        }} 
      />
      <Stack.Screen 
        name="VoiceCommands" 
        component={VoiceCommandsScreen} 
        options={{ 
          headerShown: true,
          headerStyle: { backgroundColor: '#22c55e' },
          headerTintColor: '#fff',
          title: 'Voice Commands'
        }} 
      />
      <Stack.Screen 
        name="AugmentedReality" 
        component={AugmentedRealityScreen} 
        options={{ 
          headerShown: true,
          headerStyle: { backgroundColor: '#22c55e' },
          headerTintColor: '#fff',
          title: 'Augmented Reality'
        }} 
      />
      <Stack.Screen 
        name="EquipmentDiagnostics" 
        component={EquipmentDiagnosticsScreen} 
        options={{ 
          headerShown: true,
          headerStyle: { backgroundColor: '#22c55e' },
          headerTintColor: '#fff',
          title: 'Equipment Diagnostics'
        }} 
      />
    </Stack.Navigator>
  );
};

export default MainNavigator;
