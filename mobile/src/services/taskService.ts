import api from './apiClient';

// Task types
export type Comment = {
  id: string;
  text: string;
  createdAt: string;
  createdBy: string;
};

export type Task = {
  id: string;
  title: string;
  description?: string;
  status: 'pending' | 'in-progress' | 'completed' | 'cancelled';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  dueDate: string;
  assignedTo?: string;
  fieldId?: string;
  fieldName?: string;
  equipmentId?: string;
  equipmentName?: string;
  createdAt: string;
  createdBy?: string;
  comments?: Comment[];
};

export type TaskWithDetails = Task & {
  comments: Comment[];
};

// Task service methods
export const taskService = {
  // Get all tasks for a farm
  getTasks: async (farmId: string): Promise<Task[]> => {
    try {
      if (!farmId) {
        throw new Error('No farm ID provided');
      }

      const response = await api.get<{ tasks: Task[] }>(`/tasks/farm/${farmId}`);
      return response.data.tasks;
    } catch (error) {
      console.error('Error fetching tasks:', error);
      // Return empty array in case of error
      return [];
    }
  },

  // Get a single task by ID with comments
  getTaskById: async (taskId: string): Promise<TaskWithDetails | null> => {
    try {
      const response = await api.get<{ task: TaskWithDetails }>(`/tasks/${taskId}`);
      return response.data.task;
    } catch (error) {
      console.error(`Error fetching task ${taskId}:`, error);
      // Return null in case of error
      return null;
    }
  },

  // Get comments for a task
  getTaskComments: async (taskId: string): Promise<Comment[]> => {
    try {
      const response = await api.get<{ comments: Comment[] }>(`/tasks/${taskId}/comments`);
      return response.data.comments;
    } catch (error) {
      console.error(`Error fetching comments for task ${taskId}:`, error);
      // Return empty array in case of error
      return [];
    }
  },

  // Create a new task
  createTask: async (task: Omit<Task, 'id' | 'createdAt'>): Promise<Task | null> => {
    try {
      const response = await api.post<{ task: Task }>('/tasks', task);
      return response.data.task;
    } catch (error) {
      console.error('Error creating task:', error);
      // Return null in case of error
      return null;
    }
  },

  // Update an existing task
  updateTask: async (taskId: string, task: Partial<Task>): Promise<Task | null> => {
    try {
      const response = await api.put<{ task: Task }>(`/tasks/${taskId}`, task);
      return response.data.task;
    } catch (error) {
      console.error(`Error updating task ${taskId}:`, error);
      // Return null in case of error
      return null;
    }
  },

  // Update task status
  updateTaskStatus: async (taskId: string, status: Task['status']): Promise<Task | null> => {
    try {
      const response = await api.patch<{ task: Task }>(`/tasks/${taskId}/status`, { status });
      return response.data.task;
    } catch (error) {
      console.error(`Error updating status for task ${taskId}:`, error);
      // Return null in case of error
      return null;
    }
  },

  // Delete a task
  deleteTask: async (taskId: string): Promise<boolean> => {
    try {
      await api.delete(`/tasks/${taskId}`);
      return true;
    } catch (error) {
      console.error(`Error deleting task ${taskId}:`, error);
      // Return false in case of error
      return false;
    }
  },

  // Add a comment to a task
  addTaskComment: async (
    taskId: string, 
    comment: Omit<Comment, 'id' | 'createdAt'>
  ): Promise<Comment | null> => {
    try {
      const response = await api.post<{ comment: Comment }>(`/tasks/${taskId}/comments`, comment);
      return response.data.comment;
    } catch (error) {
      console.error(`Error adding comment to task ${taskId}:`, error);
      // Return null in case of error
      return null;
    }
  },

  // Delete a comment
  deleteTaskComment: async (taskId: string, commentId: string): Promise<boolean> => {
    try {
      await api.delete(`/tasks/${taskId}/comments/${commentId}`);
      return true;
    } catch (error) {
      console.error(`Error deleting comment ${commentId} from task ${taskId}:`, error);
      // Return false in case of error
      return false;
    }
  }
};

export default taskService;