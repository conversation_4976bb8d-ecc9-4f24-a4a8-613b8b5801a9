import * as Network from 'expo-network';
import { Platform } from 'react-native';
import { useState, useEffect, useCallback } from 'react';

/**
 * Network service for monitoring connectivity status
 * This service provides methods for checking network status and subscribing to network status changes
 */

// Type definitions
export type NetworkStatus = {
  isConnected: boolean;
  isInternetReachable: boolean | null;
  type: Network.NetworkStateType | null;
};

// Event listeners
type NetworkStatusListener = (status: NetworkStatus) => void;
const listeners: NetworkStatusListener[] = [];

// Current network status
let currentStatus: NetworkStatus = {
  isConnected: false,
  isInternetReachable: null,
  type: null,
};

// Check if the device is connected to the internet
const isConnected = async (): Promise<boolean> => {
  try {
    const networkState = await Network.getNetworkStateAsync();
    return networkState.isConnected && (networkState.isInternetReachable !== false);
  } catch (error) {
    console.error('Error checking network connection:', error);
    return false;
  }
};

// Get the current network status
const getNetworkStatus = async (): Promise<NetworkStatus> => {
  try {
    const networkState = await Network.getNetworkStateAsync();
    currentStatus = {
      isConnected: networkState.isConnected,
      isInternetReachable: networkState.isInternetReachable,
      type: networkState.type,
    };
    return currentStatus;
  } catch (error) {
    console.error('Error getting network status:', error);
    return currentStatus;
  }
};

// Add a listener for network status changes
const addNetworkStatusListener = (listener: NetworkStatusListener): (() => void) => {
  listeners.push(listener);
  
  // Return a function to remove the listener
  return () => {
    const index = listeners.indexOf(listener);
    if (index !== -1) {
      listeners.splice(index, 1);
    }
  };
};

// Notify all listeners of a network status change
const notifyListeners = (status: NetworkStatus): void => {
  listeners.forEach(listener => {
    try {
      listener(status);
    } catch (error) {
      console.error('Error notifying network status listener:', error);
    }
  });
};

// Start monitoring network status
let networkMonitoringStarted = false;
const startNetworkMonitoring = async (): Promise<void> => {
  if (networkMonitoringStarted) {
    return;
  }
  
  networkMonitoringStarted = true;
  
  // Get initial network status
  await getNetworkStatus();
  
  // Set up polling for network status changes
  // This is a workaround since expo-network doesn't provide a native event listener
  const pollNetworkStatus = async () => {
    try {
      const newStatus = await getNetworkStatus();
      
      // Check if the status has changed
      if (
        newStatus.isConnected !== currentStatus.isConnected ||
        newStatus.isInternetReachable !== currentStatus.isInternetReachable ||
        newStatus.type !== currentStatus.type
      ) {
        // Update current status
        currentStatus = newStatus;
        
        // Notify listeners
        notifyListeners(currentStatus);
      }
    } catch (error) {
      console.error('Error polling network status:', error);
    }
  };
  
  // Poll every 5 seconds
  setInterval(pollNetworkStatus, 5000);
  
  // Also poll when the app comes to the foreground
  // This would typically be handled with AppState, but for simplicity we'll just poll regularly
};

// React hook for network status
export const useNetworkStatus = (): NetworkStatus => {
  const [status, setStatus] = useState<NetworkStatus>(currentStatus);
  
  const handleStatusChange = useCallback((newStatus: NetworkStatus) => {
    setStatus(newStatus);
  }, []);
  
  useEffect(() => {
    // Start monitoring if not already started
    startNetworkMonitoring();
    
    // Add listener
    const removeListener = addNetworkStatusListener(handleStatusChange);
    
    // Get current status
    getNetworkStatus().then(handleStatusChange);
    
    // Clean up
    return () => {
      removeListener();
    };
  }, [handleStatusChange]);
  
  return status;
};

// Export the network service
const networkService = {
  isConnected,
  getNetworkStatus,
  addNetworkStatusListener,
  startNetworkMonitoring,
  useNetworkStatus,
};

export default networkService;