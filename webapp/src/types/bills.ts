export interface BillCategory {
  id: string;
  name: string;
  description?: string;
  color?: string;
}

export interface Vendor {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  address?: string;
}

export interface BillPayment {
  id: string;
  amount: number;
  payment_date: string;
  payment_method?: string;
  reference_number?: string;
  notes?: string;
  created_by?: string;
  created_at: string;
  creator?: {
    id: string;
    first_name: string;
    last_name: string;
  };
}

export interface BillTransaction {
  id: string;
  amount: number;
  linked_at: string;
  notes?: string;
  transaction: {
    id: string;
    transaction_date: string;
    description: string;
    amount: number;
    transaction_type: string;
  };
  linkedBy?: {
    id: string;
    first_name: string;
    last_name: string;
  };
}

export interface BillAttachment {
  id: string;
  file_name: string;
  file_path: string;
  file_type?: string;
  file_size?: number;
  uploaded_at: string;
  uploader?: {
    id: string;
    first_name: string;
    last_name: string;
  };
}

export interface RecurringSchedule {
  id: string;
  frequency: string;
  start_date: string;
  end_date?: string;
  next_due_date: string;
  day_of_month?: number;
  day_of_week?: number;
  week_of_month?: number;
  month_of_year?: number;
}

export interface Bill {
  id: string;
  farm_id: string;
  category_id?: string;
  vendor_id?: string;
  title: string;
  description?: string;
  amount: number;
  due_date: string;
  status: string;
  payment_method?: string;
  reference_number?: string;
  notes?: string;
  is_recurring: boolean;
  created_by?: string;
  created_at: string;
  updated_at: string;
  category?: BillCategory;
  vendor?: Vendor;
  recurringSchedule?: RecurringSchedule;
  payments?: BillPayment[];
  transactions?: BillTransaction[];
  attachments?: BillAttachment[];
  creator?: {
    id: string;
    first_name: string;
    last_name: string;
  };
}

export interface BillFormData {
  farmId: string;
  categoryId?: string;
  vendorId?: string;
  title: string;
  description?: string;
  amount: number;
  dueDate: string;
  status: string;
  paymentMethod?: string;
  referenceNumber?: string;
  notes?: string;
  isRecurring: boolean;
  recurringSchedule?: {
    frequency: string;
    startDate: string;
    endDate?: string;
    dayOfMonth?: number;
    dayOfWeek?: number;
    weekOfMonth?: number;
    monthOfYear?: number;
    nextDueDate: string;
  };
}

export interface BillCategoryFormData {
  farmId: string;
  name: string;
  description?: string;
  color?: string;
}

export interface BillPaymentFormData {
  amount: number;
  paymentDate: string;
  paymentMethod?: string;
  referenceNumber?: string;
  notes?: string;
}

export interface BillTransactionLinkFormData {
  transactionId: string;
  amount: number;
  notes?: string;
}

export interface BillStatistics {
  totalBills: number;
  paidBills: number;
  unpaidBills: number;
  partialBills: number;
  overdueBills: number;
  upcomingBills: number;
  billsByCategory: {
    category_id: string;
    total_amount: number;
    category: {
      name: string;
      color: string;
    };
  }[];
}