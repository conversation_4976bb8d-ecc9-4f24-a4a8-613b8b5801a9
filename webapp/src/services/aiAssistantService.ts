import axios from 'axios';
import { API_URL } from '../config';
import { getAuthToken } from '../utils/storageUtils';

// Types for Decision Support System
export interface DecisionSupport {
  id: number;
  farm_id: string;
  operation_type: string;
  recommendation: string;
  confidence_score: number;
  factors_considered: string;
  is_implemented: boolean;
  created_at: string;
  updated_at: string;
}

// Types for Predictive Maintenance
export interface PredictiveMaintenance {
  id: number;
  farm_id: string;
  equipment_id: string;
  equipment_name?: string;
  maintenance_type: string;
  prediction: string;
  urgency_level: string;
  predicted_failure_date: string | null;
  confidence_score: number;
  is_addressed: boolean;
  created_at: string;
  updated_at: string;
}

// Types for Harvest Recommendations
export interface HarvestRecommendation {
  id: number;
  farm_id: string;
  field_id: string;
  field_name?: string;
  crop_id: string;
  crop_name?: string;
  crop_type: string;
  harvest_date: string;
  explanation: string;
  factors_considered: string;
  recommended_date: string;
  recommendation_reason: string;
  confidence_score: number;
  weather_factors: string;
  market_factors: string;
  is_implemented: boolean;
  created_at: string;
  updated_at: string;
}

// Types for Field Improvement Recommendations
export interface FieldImprovementRecommendation {
  id: number;
  farm_id: string;
  field_id: string;
  field_name?: string;
  improvement_type: string;
  recommendation: string;
  expected_benefit: string;
  confidence_score: number;
  is_implemented: boolean;
  created_at: string;
  updated_at: string;
}

// Types for Financial Recommendations
export interface FinancialRecommendation {
  id: number;
  farm_id: string;
  category: string;
  recommendation_type: string;
  recommendation: string;
  implementation_difficulty: string;
  financial_impact: string;
  potential_savings: number;
  implementation_cost: number;
  roi_period: string;
  confidence_score: number;
  is_implemented: boolean;
  created_at: string;
  updated_at: string;
}

// Types for Yield Profit Recommendations
export interface YieldProfitRecommendation {
  id: number;
  farm_id: string;
  field_id: string;
  field_name?: string;
  crop_id: string;
  crop_name?: string;
  category: string;
  implementation_timeframe: string;
  yield_impact: string;
  profit_impact: string;
  recommendation: string;
  expected_yield_increase: string;
  expected_profit_increase: string;
  implementation_steps: string;
  confidence_score: number;
  is_implemented: boolean;
  created_at: string;
  updated_at: string;
}

/**
 * Get AI assistant response for a query
 * @param query - The user's query
 * @param farmId - The ID of the current farm
 * @returns The AI assistant's response
 */
export const getAIResponse = async (query: string, farmId: string): Promise<string> => {
  try {
    const response = await axios.post(
      `${API_URL}/ai-assistant/query`,
      {
        query,
        farmId
      },
      {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      }
    );
    return response.data.response;
  } catch (error) {
    console.error('Error getting AI response:', error);
    throw error;
  }
};

/**
 * Get AI assistant query history for a farm
 * @param farmId - The ID of the current farm
 * @returns The query history
 */
export const getQueryHistory = async (farmId: string): Promise<any[]> => {
  try {
    const response = await axios.get(`${API_URL}/ai-assistant/history/${farmId}`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
    return response.data.queries;
  } catch (error) {
    console.error('Error fetching query history:', error);
    throw error;
  }
};

/**
 * Get AI assistant suggestions based on farm data
 * @param farmId - The ID of the current farm
 * @returns The AI assistant's suggestions
 */
export const getSuggestions = async (farmId: string): Promise<string[]> => {
  try {
    const response = await axios.get(`${API_URL}/ai-assistant/suggestions/${farmId}`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
    return response.data.suggestions;
  } catch (error) {
    console.error('Error fetching suggestions:', error);
    throw error;
  }
};

/**
 * Get AI decision support recommendations for a farm
 * @param farmId - The ID of the current farm
 * @returns The decision support recommendations
 */
export const getDecisionSupport = async (farmId: string): Promise<DecisionSupport[]> => {
  try {
    const response = await axios.get(`${API_URL}/ai-assistant/decision-support/${farmId}`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
    return response.data.recommendations;
  } catch (error) {
    console.error('Error fetching decision support recommendations:', error);
    throw error;
  }
};

/**
 * Create a new AI decision support recommendation
 * @param farmId - The ID of the current farm
 * @param operationType - The type of operation
 * @param recommendation - The recommendation text
 * @param confidenceScore - The confidence score (0-100)
 * @param factorsConsidered - The factors considered in the recommendation
 * @returns The created recommendation
 */
export const createDecisionSupport = async (
  farmId: string,
  operationType: string,
  recommendation: string,
  confidenceScore: number,
  factorsConsidered: string
): Promise<DecisionSupport> => {
  try {
    const response = await axios.post(
      `${API_URL}/ai-assistant/decision-support`,
      {
        farmId,
        operationType,
        recommendation,
        confidenceScore,
        factorsConsidered
      },
      {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      }
    );
    return response.data.recommendation;
  } catch (error) {
    console.error('Error creating decision support recommendation:', error);
    throw error;
  }
};

/**
 * Update an AI decision support recommendation
 * @param id - The ID of the recommendation
 * @param isImplemented - Whether the recommendation has been implemented
 * @returns The updated recommendation
 */
export const updateDecisionSupport = async (
  id: number,
  isImplemented: boolean
): Promise<DecisionSupport> => {
  try {
    const response = await axios.put(
      `${API_URL}/ai-assistant/decision-support/${id}`,
      {
        isImplemented
      },
      {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      }
    );
    return response.data.recommendation;
  } catch (error) {
    console.error('Error updating decision support recommendation:', error);
    throw error;
  }
};

/**
 * Get AI predictive maintenance recommendations for a farm
 * @param farmId - The ID of the current farm
 * @returns The predictive maintenance recommendations
 */
export const getPredictiveMaintenance = async (farmId: string): Promise<PredictiveMaintenance[]> => {
  try {
    const response = await axios.get(`${API_URL}/ai-assistant/predictive-maintenance/${farmId}`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
    return response.data.predictions;
  } catch (error) {
    console.error('Error fetching predictive maintenance recommendations:', error);
    throw error;
  }
};

/**
 * Create a new AI predictive maintenance recommendation
 * @param farmId - The ID of the current farm
 * @param equipmentId - The ID of the equipment
 * @param maintenanceType - The type of maintenance
 * @param prediction - The prediction text
 * @param urgencyLevel - The urgency level (High, Medium, Low)
 * @param predictedFailureDate - The predicted failure date (optional)
 * @param confidenceScore - The confidence score (0-100)
 * @returns The created prediction
 */
export const createPredictiveMaintenance = async (
  farmId: string,
  equipmentId: string,
  maintenanceType: string,
  prediction: string,
  urgencyLevel: string,
  confidenceScore: number,
  predictedFailureDate?: string
): Promise<PredictiveMaintenance> => {
  try {
    const response = await axios.post(
      `${API_URL}/ai-assistant/predictive-maintenance`,
      {
        farmId,
        equipmentId,
        maintenanceType,
        prediction,
        urgencyLevel,
        confidenceScore,
        predictedFailureDate
      },
      {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      }
    );
    return response.data.prediction;
  } catch (error) {
    console.error('Error creating predictive maintenance recommendation:', error);
    throw error;
  }
};

/**
 * Update an AI predictive maintenance recommendation
 * @param id - The ID of the prediction
 * @param isAddressed - Whether the prediction has been addressed
 * @returns The updated prediction
 */
export const updatePredictiveMaintenance = async (
  id: number,
  isAddressed: boolean
): Promise<PredictiveMaintenance> => {
  try {
    const response = await axios.put(
      `${API_URL}/ai-assistant/predictive-maintenance/${id}`,
      {
        isAddressed
      },
      {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      }
    );
    return response.data.prediction;
  } catch (error) {
    console.error('Error updating predictive maintenance recommendation:', error);
    throw error;
  }
};

/**
 * Get AI harvest timing recommendations for a farm
 * @param farmId - The ID of the current farm
 * @returns The harvest recommendations
 */
export const getHarvestRecommendations = async (farmId: string): Promise<HarvestRecommendation[]> => {
  try {
    const response = await axios.get(`${API_URL}/ai-assistant/harvest-recommendations/${farmId}`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
    return response.data.recommendations;
  } catch (error) {
    console.error('Error fetching harvest recommendations:', error);
    throw error;
  }
};

/**
 * Update an AI harvest recommendation
 * @param id - The ID of the recommendation
 * @param isImplemented - Whether the recommendation has been implemented
 * @returns The updated recommendation
 */
export const updateHarvestRecommendation = async (
  id: number,
  isImplemented: boolean
): Promise<HarvestRecommendation> => {
  try {
    const response = await axios.put(
      `${API_URL}/ai-assistant/harvest-recommendations/${id}`,
      {
        isImplemented
      },
      {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      }
    );
    return response.data.recommendation;
  } catch (error) {
    console.error('Error updating harvest recommendation:', error);
    throw error;
  }
};

/**
 * Get AI field improvement recommendations for a farm
 * @param farmId - The ID of the current farm
 * @returns The field improvement recommendations
 */
export const getFieldImprovementRecommendations = async (farmId: string): Promise<FieldImprovementRecommendation[]> => {
  try {
    const response = await axios.get(`${API_URL}/ai-assistant/field-improvement-recommendations/${farmId}`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
    return response.data.recommendations;
  } catch (error) {
    console.error('Error fetching field improvement recommendations:', error);
    throw error;
  }
};

/**
 * Update an AI field improvement recommendation
 * @param id - The ID of the recommendation
 * @param isImplemented - Whether the recommendation has been implemented
 * @returns The updated recommendation
 */
export const updateFieldImprovementRecommendation = async (
  id: number,
  isImplemented: boolean
): Promise<FieldImprovementRecommendation> => {
  try {
    const response = await axios.put(
      `${API_URL}/ai-assistant/field-improvement-recommendations/${id}`,
      {
        isImplemented
      },
      {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      }
    );
    return response.data.recommendation;
  } catch (error) {
    console.error('Error updating field improvement recommendation:', error);
    throw error;
  }
};

/**
 * Get AI financial optimization recommendations for a farm
 * @param farmId - The ID of the current farm
 * @returns The financial recommendations
 */
export const getFinancialRecommendations = async (farmId: string): Promise<FinancialRecommendation[]> => {
  try {
    const response = await axios.get(`${API_URL}/ai-assistant/financial-recommendations/${farmId}`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
    return response.data.recommendations;
  } catch (error) {
    console.error('Error fetching financial recommendations:', error);
    throw error;
  }
};

/**
 * Update an AI financial recommendation
 * @param id - The ID of the recommendation
 * @param isImplemented - Whether the recommendation has been implemented
 * @returns The updated recommendation
 */
export const updateFinancialRecommendation = async (
  id: number,
  isImplemented: boolean
): Promise<FinancialRecommendation> => {
  try {
    const response = await axios.put(
      `${API_URL}/ai-assistant/financial-recommendations/${id}`,
      {
        isImplemented
      },
      {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      }
    );
    return response.data.recommendation;
  } catch (error) {
    console.error('Error updating financial recommendation:', error);
    throw error;
  }
};

/**
 * Get AI yield and profit maximization recommendations for a farm
 * @param farmId - The ID of the current farm
 * @returns The yield and profit recommendations
 */
export const getYieldProfitRecommendations = async (farmId: string): Promise<YieldProfitRecommendation[]> => {
  try {
    const response = await axios.get(`${API_URL}/ai-assistant/yield-profit-recommendations/${farmId}`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
    return response.data.recommendations;
  } catch (error) {
    console.error('Error fetching yield and profit recommendations:', error);
    throw error;
  }
};

/**
 * Update an AI yield and profit maximization recommendation
 * @param id - The ID of the recommendation
 * @param isImplemented - Whether the recommendation has been implemented
 * @returns The updated recommendation
 */
export const updateYieldProfitRecommendation = async (
  id: number,
  isImplemented: boolean
): Promise<YieldProfitRecommendation> => {
  try {
    const response = await axios.put(
      `${API_URL}/ai-assistant/yield-profit-recommendations/${id}`,
      {
        isImplemented
      },
      {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      }
    );
    return response.data.recommendation;
  } catch (error) {
    console.error('Error updating yield and profit recommendation:', error);
    throw error;
  }
};
