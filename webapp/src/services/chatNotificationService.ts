import axios from 'axios';
import { API_URL } from '../config';
import { createAlertRule } from './alertService';

/**
 * Service for handling chat notifications, including email notifications for unread messages
 */

/**
 * Send an email notification for unread chat messages
 * @param userId The ID of the user who has unread messages
 * @param conversationId The ID of the conversation with unread messages
 * @param messageCount The number of unread messages
 * @returns Promise<void>
 */
export const sendUnreadMessagesEmail = async (
  userId: string,
  conversationId: string,
  messageCount: number
): Promise<void> => {
  try {
    await axios.post(`${API_URL}/chat/notifications/email`, {
      userId,
      conversationId,
      messageCount
    });
  } catch (error) {
    console.error('Error sending unread messages email notification:', error);
    throw new Error('Failed to send email notification for unread messages');
  }
};

/**
 * Create an alert rule for sending email notifications for unread chat messages
 * @param farmId The ID of the farm
 * @param userId The ID of the user to send notifications to
 * @param email The email address to send notifications to
 * @returns Promise<void>
 */
export const createUnreadMessagesAlertRule = async (
  farmId: string,
  userId: string,
  email: string
): Promise<void> => {
  try {
    // Create an alert rule for unread chat messages
    await createAlertRule({
      name: 'Unread Chat Messages',
      description: 'Send email notifications for unread chat messages after 30 minutes',
      enabled: true,
      conditions: [
        {
          id: 'condition1',
          type: 'chat_message',
          parameter: 'unread_time',
          operator: 'greater_than',
          value: '30' // 30 minutes
        }
      ],
      actions: [
        {
          id: 'action1',
          type: 'email',
          recipients: [email],
          template: 'unread_chat_messages'
        }
      ],
      farmId
    });
  } catch (error) {
    console.error('Error creating unread messages alert rule:', error);
    throw new Error('Failed to create alert rule for unread messages');
  }
};

/**
 * Check for unread messages and send email notifications if needed
 * This function would be called by a cron job or similar scheduled task
 * @param farmId The ID of the farm to check unread messages for
 * @returns Promise<void>
 */
export const checkAndSendUnreadMessageNotifications = async (farmId: string): Promise<void> => {
  try {
    // Get all users with unread messages older than 30 minutes
    const response = await axios.get(`${API_URL}/chat/unread-messages/check?farmId=${farmId}`);

    // For each user with unread messages, send an email notification
    for (const user of response.data.users) {
      await sendUnreadMessagesEmail(
        user.id,
        user.conversationId,
        user.unreadCount
      );
    }
  } catch (error) {
    console.error('Error checking and sending unread message notifications:', error);
    throw new Error('Failed to check and send unread message notifications');
  }
};
