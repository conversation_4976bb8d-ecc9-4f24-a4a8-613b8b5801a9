import axios from 'axios';
import { API_URL } from '../config';
import { getAuthToken } from '../utils/storageUtils';

// Types for weather data
export interface CurrentWeather {
  temp: number;
  condition: string;
  icon: string;
}

export interface ForecastDay {
  day: string;
  high: number;
  low: number;
  condition: string;
  icon: string;
}

export interface HourlyForecast {
  time: string;
  temp: number;
  condition: string;
  icon: string;
  wind_speed: string;
  wind_direction: string;
  precipitation_chance: number;
  humidity: number;
}

export interface WeatherAlert {
  id: string;
  event: string;
  headline: string;
  description: string;
  instruction: string;
  severity: string;
  certainty: string;
  urgency: string;
  onset: string;
  expires: string;
  status: string;
  messageType: string;
  category: string;
  response: string;
  source: string;
}

export interface WeatherAlertData {
  alerts: WeatherAlert[];
  count: number;
  location: string;
}

export interface HistoricalWeatherRecord {
  interval_start: string;
  temperature_avg: number | null;
  precipitation_avg: number | null;
  humidity_avg: number | null;
  wind_speed_avg: number | null;
  most_common_condition: string | null;
}

export interface HistoricalWeatherAnalysis {
  temperature_trend: 'increasing' | 'decreasing' | 'stable' | 'insufficient_data';
  precipitation_trend: 'increasing' | 'decreasing' | 'stable' | 'insufficient_data';
  humidity_trend: 'increasing' | 'decreasing' | 'stable' | 'insufficient_data';
  wind_speed_trend: 'increasing' | 'decreasing' | 'stable' | 'insufficient_data';
  condition_summary: Record<string, number>;
  temperature_avg?: number | null;
  precipitation_avg?: number | null;
  humidity_avg?: number | null;
  wind_speed_avg?: number | null;
}

export interface HistoricalWeatherData {
  data: HistoricalWeatherRecord[];
  analysis: HistoricalWeatherAnalysis;
}

export interface WeatherData {
  current: CurrentWeather;
  forecast: ForecastDay[];
  hourly?: HourlyForecast[];
}

// Create a proxy endpoint on your server or use a CORS proxy
const WEATHER_API_BASE = `${API_URL}/weather/proxy`;

/**
 * Interface for all weather data
 */
export interface AllWeatherData {
  current: CurrentWeather;
  forecast: ForecastDay[];
  detailed_forecast: any[];
  hourly: HourlyForecast[];
  alerts: WeatherAlert[];
  historical: {
    data: HistoricalWeatherRecord[];
    analysis: HistoricalWeatherAnalysis;
  };
}

/**
 * Get all weather data for a field in a single request
 * @param fieldId The field ID
 * @param params Optional parameters (days, hours, startDate, endDate, interval)
 * @returns Promise<AllWeatherData> All weather data for the field
 */
export const getFieldAllWeatherData = async (
  fieldId: string,
  params: {
    days?: number;
    hours?: number;
    startDate?: string;
    endDate?: string;
    interval?: 'hourly' | 'daily' | 'weekly' | 'monthly';
  } = {}
): Promise<AllWeatherData> => {
  try {
    const token = getAuthToken();

    // Build query string
    const queryParams = new URLSearchParams();

    if (params.days) queryParams.append('days', params.days.toString());
    if (params.hours) queryParams.append('hours', params.hours.toString());
    if (params.startDate) queryParams.append('startDate', params.startDate);
    if (params.endDate) queryParams.append('endDate', params.endDate);
    if (params.interval) queryParams.append('interval', params.interval);

    const queryString = queryParams.toString();
    const url = `${API_URL}/weather/all/field/${fieldId}${queryString ? `?${queryString}` : ''}`;

    const response = await axios.get(url, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    return response.data;
  } catch (error) {
    console.error('Error fetching all field weather data:', error);
    throw error;
  }
};

/**
 * Get all weather data for a farm in a single request
 * @param farmId The farm ID
 * @param params Optional parameters (days, hours, startDate, endDate, interval)
 * @returns Promise<AllWeatherData> All weather data for the farm
 */
export const getFarmAllWeatherData = async (
  farmId: string,
  params: {
    days?: number;
    hours?: number;
    startDate?: string;
    endDate?: string;
    interval?: 'hourly' | 'daily' | 'weekly' | 'monthly';
  } = {}
): Promise<AllWeatherData> => {
  try {
    const token = getAuthToken();

    // Build query string
    const queryParams = new URLSearchParams();

    if (params.days) queryParams.append('days', params.days.toString());
    if (params.hours) queryParams.append('hours', params.hours.toString());
    if (params.startDate) queryParams.append('startDate', params.startDate);
    if (params.endDate) queryParams.append('endDate', params.endDate);
    if (params.interval) queryParams.append('interval', params.interval);

    const queryString = queryParams.toString();
    const url = `${API_URL}/weather/all/farm/${farmId}${queryString ? `?${queryString}` : ''}`;

    const response = await axios.get(url, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    return response.data;
  } catch (error) {
    console.error('Error fetching all farm weather data:', error);
    throw error;
  }
};

/**
 * Get weather data for the current location
 * @returns Promise<WeatherData> Weather data for the current location
 */
export const getWeatherForCurrentLocation = async (): Promise<WeatherData> => {
  try {
    // Get current location using browser's geolocation API
    const position = await new Promise<GeolocationPosition>((resolve, reject) => {
      navigator.geolocation.getCurrentPosition(resolve, reject);
    });

    const { latitude, longitude } = position.coords;

    // Create a virtual farm ID for the current location
    // This allows us to use the more robust farm weather endpoint
    const virtualFarmId = `current_location_${latitude.toFixed(4)}_${longitude.toFixed(4)}`;

    // First try to use the enhanced weather endpoint
    try {
      const token = getAuthToken();

      // Try to use the all weather data endpoint for more comprehensive data
      const allWeatherResponse = await axios.get(
        `${API_URL}/weather/all/farm/${virtualFarmId}?lat=${latitude}&lon=${longitude}`,
        {
          headers: token ? { Authorization: `Bearer ${token}` } : {}
        }
      );

      // Return the comprehensive data
      return {
        current: allWeatherResponse.data.current,
        forecast: allWeatherResponse.data.forecast,
        hourly: allWeatherResponse.data.hourly
      };
    } catch (enhancedApiError) {
      console.warn('Enhanced weather API failed, falling back to proxy endpoint:', enhancedApiError);

      // Fall back to the legacy proxy endpoint
      // Add timestamp as cache-busting parameter
      const timestamp = new Date().getTime();
      const response = await axios.get(
        `${WEATHER_API_BASE}?lat=${latitude}&lon=${longitude}&units=imperial&_t=${timestamp}`
      );

      // Process the response data
      const { current, forecast, hourly } = response.data;
      return { current, forecast, hourly };
    }
  } catch (error) {
    console.error('Error fetching weather data:', error);

    // Return mock data as fallback
    return {
      current: { temp: 72, condition: 'Sunny', icon: '01d' },
      forecast: [
        { day: 'Today', high: 75, low: 62, condition: 'Sunny', icon: '01d' },
        { day: 'Tomorrow', high: 78, low: 64, condition: 'Partly Cloudy', icon: '02d' },
        { day: 'Wed', high: 80, low: 66, condition: 'Cloudy', icon: '03d' },
      ],
      hourly: Array.from({ length: 24 }, (_, i) => {
        const hour = new Date();
        hour.setHours(hour.getHours() + i);
        return {
          time: hour.toISOString(),
          temp: Math.round(70 + Math.random() * 10), // Random temp between 70-80
          condition: ['Sunny', 'Partly Cloudy', 'Cloudy', 'Rainy'][Math.floor(Math.random() * 4)],
          icon: ['01d', '02d', '03d', '10d'][Math.floor(Math.random() * 4)],
          wind_speed: `${Math.round(5 + Math.random() * 15)} mph`, // Random wind speed
          wind_direction: ['N', 'NE', 'E', 'SE', 'S', 'SW', 'W', 'NW'][Math.floor(Math.random() * 8)],
          precipitation_chance: Math.round(Math.random() * 100), // Random precipitation chance
          humidity: Math.round(30 + Math.random() * 50) // Random humidity between 30-80
        };
      })
    };
  }
};

/**
 * Get weather data for a specific location (farm address)
 * @param address The address string (e.g. "123 Main St, City, State, Zip")
 * @returns Promise<WeatherData> Weather data for the specified location
 */
export const getWeatherForAddress = async (address: string, city: string, state: string, zip: string): Promise<WeatherData> => {
  try {
    // Geocode the address to get latitude and longitude
    // For simplicity, we'll use a mock geocoding function
    // In a real application, you would use a geocoding service like Google Maps API
    const { latitude, longitude } = await geocodeAddress(address, city, state, zip);

    // Create a virtual farm ID for the address location
    // This allows us to use the more robust farm weather endpoint
    const virtualFarmId = `address_${zip}_${latitude.toFixed(4)}_${longitude.toFixed(4)}`;

    // First try to use the enhanced weather endpoint
    try {
      const token = getAuthToken();

      // Try to use the all weather data endpoint for more comprehensive data
      const allWeatherResponse = await axios.get(
        `${API_URL}/weather/all/farm/${virtualFarmId}?lat=${latitude}&lon=${longitude}`,
        {
          headers: token ? { Authorization: `Bearer ${token}` } : {}
        }
      );

      // Return the comprehensive data
      return {
        current: allWeatherResponse.data.current,
        forecast: allWeatherResponse.data.forecast,
        hourly: allWeatherResponse.data.hourly
      };
    } catch (enhancedApiError) {
      console.warn('Enhanced weather API failed, falling back to proxy endpoint:', enhancedApiError);

      // Fall back to the legacy proxy endpoint
      // Add timestamp as cache-busting parameter
      const timestamp = new Date().getTime();
      const response = await axios.get(
        `${WEATHER_API_BASE}?lat=${latitude}&lon=${longitude}&units=imperial&_t=${timestamp}`
      );

      // Process the response data
      const { current, forecast, hourly } = response.data;
      return { current, forecast, hourly };
    }
  } catch (error) {
    console.error('Error fetching weather data for address:', error);

    // Return mock data as fallback
    return {
      current: { temp: 72, condition: 'Sunny', icon: '01d' },
      forecast: [
        { day: 'Today', high: 75, low: 62, condition: 'Sunny', icon: '01d' },
        { day: 'Tomorrow', high: 78, low: 64, condition: 'Partly Cloudy', icon: '02d' },
        { day: 'Wed', high: 80, low: 66, condition: 'Cloudy', icon: '03d' },
      ],
      hourly: Array.from({ length: 24 }, (_, i) => {
        const hour = new Date();
        hour.setHours(hour.getHours() + i);
        return {
          time: hour.toISOString(),
          temp: Math.round(70 + Math.random() * 10), // Random temp between 70-80
          condition: ['Sunny', 'Partly Cloudy', 'Cloudy', 'Rainy'][Math.floor(Math.random() * 4)],
          icon: ['01d', '02d', '03d', '10d'][Math.floor(Math.random() * 4)],
          wind_speed: `${Math.round(5 + Math.random() * 15)} mph`, // Random wind speed
          wind_direction: ['N', 'NE', 'E', 'SE', 'S', 'SW', 'W', 'NW'][Math.floor(Math.random() * 8)],
          precipitation_chance: Math.round(Math.random() * 100), // Random precipitation chance
          humidity: Math.round(30 + Math.random() * 50) // Random humidity between 30-80
        };
      })
    };
  }
};

/**
 * Geocode an address to get latitude and longitude
 * This is a mock function for demonstration purposes
 * In a real application, you would use a geocoding service like Google Maps API
 */
const geocodeAddress = async (address: string, city: string, state: string, zip: string): Promise<{latitude: number, longitude: number}> => {
  try {
    // In a real application, you would make an API call to a geocoding service
    // For now, we'll just return mock coordinates
    // This would be replaced with actual geocoding logic

    // Mock coordinates for demonstration
    return {
      latitude: 37.7749, // Example: San Francisco
      longitude: -122.4194
    };
  } catch (error) {
    console.error('Error geocoding address:', error);
    throw error;
  }
};

/**
 * Get the appropriate icon class for a weather condition
 * @param condition The weather condition from the API
 * @param icon The weather icon code from the API
 * @returns The icon class to use
 */
/**
 * Get weather alerts for a farm
 * @param farmId The farm ID
 * @returns Promise<WeatherAlertData> Weather alerts for the farm
 */
export const getFarmWeatherAlerts = async (farmId: string): Promise<WeatherAlertData> => {
  try {
    const token = getAuthToken();
    const response = await axios.get(`${API_URL}/weather-alerts/farm/${farmId}`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching farm weather alerts:', error);
    // Return empty alerts as fallback
    return {
      alerts: [],
      count: 0,
      location: 'Unknown'
    };
  }
};

/**
 * Get weather alerts for a field
 * @param fieldId The field ID
 * @returns Promise<WeatherAlertData> Weather alerts for the field
 */
export const getFieldWeatherAlerts = async (fieldId: string): Promise<WeatherAlertData> => {
  try {
    const token = getAuthToken();
    const response = await axios.get(`${API_URL}/weather-alerts/field/${fieldId}`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching field weather alerts:', error);
    // Return empty alerts as fallback
    return {
      alerts: [],
      count: 0,
      location: 'Unknown'
    };
  }
};

/**
 * Get historical weather data for a farm
 * @param farmId The farm ID
 * @param params Optional parameters (startDate, endDate, interval)
 * @returns Promise<HistoricalWeatherData> Historical weather data for the farm
 */
export const getFarmHistoricalWeather = async (
  farmId: string,
  params: {
    startDate?: string;
    endDate?: string;
    interval?: 'hourly' | 'daily' | 'weekly' | 'monthly';
  } = {}
): Promise<HistoricalWeatherData> => {
  try {
    const token = getAuthToken();

    // Build query string
    const queryParams = new URLSearchParams();

    if (params.startDate) queryParams.append('startDate', params.startDate);
    if (params.endDate) queryParams.append('endDate', params.endDate);
    if (params.interval) queryParams.append('interval', params.interval);

    const queryString = queryParams.toString();
    const url = `${API_URL}/historical-weather/farm/${farmId}${queryString ? `?${queryString}` : ''}`;

    const response = await axios.get(url, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    return response.data;
  } catch (error) {
    console.error('Error fetching farm historical weather:', error);
    // Return empty data as fallback
    return {
      data: [],
      analysis: {
        temperature_trend: 'insufficient_data',
        precipitation_trend: 'insufficient_data',
        humidity_trend: 'insufficient_data',
        wind_speed_trend: 'insufficient_data',
        condition_summary: {}
      }
    };
  }
};

/**
 * Get historical weather data for a field
 * @param fieldId The field ID
 * @param params Optional parameters (startDate, endDate, interval)
 * @returns Promise<HistoricalWeatherData> Historical weather data for the field
 */
export const getFieldHistoricalWeather = async (
  fieldId: string,
  params: {
    startDate?: string;
    endDate?: string;
    interval?: 'hourly' | 'daily' | 'weekly' | 'monthly';
  } = {}
): Promise<HistoricalWeatherData> => {
  try {
    const token = getAuthToken();

    // Build query string
    const queryParams = new URLSearchParams();

    if (params.startDate) queryParams.append('startDate', params.startDate);
    if (params.endDate) queryParams.append('endDate', params.endDate);
    if (params.interval) queryParams.append('interval', params.interval);

    const queryString = queryParams.toString();
    const url = `${API_URL}/historical-weather/field/${fieldId}${queryString ? `?${queryString}` : ''}`;

    const response = await axios.get(url, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    return response.data;
  } catch (error) {
    console.error('Error fetching field historical weather:', error);
    // Return empty data as fallback
    return {
      data: [],
      analysis: {
        temperature_trend: 'insufficient_data',
        precipitation_trend: 'insufficient_data',
        humidity_trend: 'insufficient_data',
        wind_speed_trend: 'insufficient_data',
        condition_summary: {}
      }
    };
  }
};

export const getWeatherIconClass = (condition: string, icon: string): string => {
  // Check if it's day or night
  const isNight = icon.endsWith('n');

  // Map weather conditions to icon classes
  // Works with both OpenWeatherMap and National Weather Service mapped conditions
  switch (condition) {
    case 'Clear':
    case 'Sunny':
      return isNight ? 'fa-moon' : 'fa-sun';
    case 'Clouds':
    case 'Partly Cloudy':
      if (icon === '02d' || icon === '02n') {
        return isNight ? 'fa-cloud-moon' : 'fa-cloud-sun';
      } else if (icon === '03d' || icon === '03n') {
        return 'fa-cloud';
      } else if (icon === '04d' || icon === '04n') {
        return 'fa-clouds';
      }
      return isNight ? 'fa-cloud-moon' : 'fa-cloud-sun';
    case 'Rain':
    case 'Drizzle':
    case 'Showers':
      return 'fa-cloud-rain';
    case 'Thunderstorm':
      return 'fa-bolt';
    case 'Snow':
    case 'Flurries':
      return 'fa-snowflake';
    case 'Mist':
    case 'Smoke':
    case 'Haze':
    case 'Dust':
    case 'Fog':
      return 'fa-smog';
    default:
      console.log(`Unmapped weather condition: ${condition} with icon: ${icon}`);
      return isNight ? 'fa-moon' : 'fa-sun';
  }
};
