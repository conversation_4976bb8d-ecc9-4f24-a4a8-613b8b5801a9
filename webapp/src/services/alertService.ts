import axios from 'axios';
import { API_URL } from '../config';
import { getAuthToken } from '../utils/storageUtils';

// Types for alerts
export interface Alert {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'warning' | 'critical' | 'success';
  source: 'system' | 'weather' | 'equipment' | 'inventory' | 'financial' | 'task' | 'crop';
  createdAt: string;
  read: boolean;
  farmId: string;
  userId: string;
  relatedEntityType?: string;
  relatedEntityId?: string;
  actionUrl?: string;
}

export interface AlertRule {
  id: string;
  name: string;
  description: string;
  enabled: boolean;
  conditions: AlertCondition[];
  actions: AlertAction[];
  farmId: string;
  createdAt: string;
  updatedAt: string;
}

export interface AlertCondition {
  id: string;
  type: string;
  parameter: string;
  operator: string;
  value: string;
}

export interface AlertAction {
  id: string;
  type: 'notification' | 'email' | 'sms' | 'task_creation';
  recipients?: string[];
  template?: string;
  taskDetails?: {
    title: string;
    description: string;
    dueDate: string;
    priority: 'low' | 'medium' | 'high';
    assigneeId?: string;
  };
}

/**
 * Get alerts for the current user
 * @param farmId The ID of the farm to get alerts for
 * @param options Query options (limit, offset, type, read)
 * @returns Promise<{ alerts: Alert[]; total: number }> Alerts and total count
 */
import { handleApiError, StructuredError } from '../utils/errorHandler';

export const getAlerts = async (
  farmId: string,
  options: {
    limit?: number;
    offset?: number;
    type?: string;
    read?: boolean;
  } = {}
): Promise<{ alerts: Alert[]; total: number }> => {
  try {
    const { limit = 10, offset = 0, type, read } = options;
    // Ensure the URL matches the expected format: https://api.nxtacre.com/alerts?farmId=...
    let url = `${API_URL}/alerts?farmId=${farmId}&limit=${limit}&offset=${offset}`;

    if (type) url += `&type=${type}`;
    if (read !== undefined) url += `&read=${read}`;

    // Get the token from storage to ensure it's included in the request
    const token = getAuthToken();

    console.log('Fetching alerts with URL:', url);

    // Make the request with explicit headers
    const response = await axios.get(url, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      // Add timeout to prevent hanging requests
      timeout: 10000
    });
    return response.data;
  } catch (error: unknown) {
    console.error('Error fetching alerts:', error);

    // Check for specific error conditions
    if (axios.isAxiosError(error)) {
      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        console.error('Response error data:', error.response.data);
        console.error('Response error status:', error.response.status);
        console.error('Response error headers:', error.response.headers);

        // Handle specific status codes
        if (error.response.status === 401) {
          console.error('Authentication error: Token might be invalid or expired');
        } else if (error.response.status === 403) {
          console.error('Authorization error: User might not have permission to access alerts');
        } else if (error.response.status === 404) {
          console.error('Not found error: The alerts endpoint might not exist');
        }
      } else if (error.request) {
        // The request was made but no response was received
        console.error('No response received from server:', error.request);
      } else {
        // Something happened in setting up the request that triggered an Error
        console.error('Error setting up the request:', error.message);
      }
    }

    // Use the new error handler to get a structured error
    const structuredError = handleApiError(error, undefined, 'Failed to fetch alerts');

    // Log the structured error for debugging
    console.error('Structured error:', structuredError);

    // Return a fallback object with empty alerts instead of throwing an error
    // This allows the UI to handle the error gracefully
    return {
      alerts: [],
      total: 0
    };
  }
};

/**
 * Mark an alert as read
 * @param alertId The ID of the alert to mark as read
 * @returns Promise<{ message: string }> Success message
 */
export const markAlertAsRead = async (alertId: string): Promise<{ message: string }> => {
  try {
    const response = await axios.put(`${API_URL}/alerts/${alertId}/read`);
    return response.data;
  } catch (error: unknown) {
    console.error('Error marking alert as read:', error);

    // Use the new error handler to get a structured error
    const structuredError = handleApiError(error, undefined, 'Failed to mark alert as read');

    // Log the structured error for debugging
    console.error('Structured error:', structuredError);

    // Return a fallback message instead of throwing an error
    return { message: 'Failed to mark alert as read. Please try again later.' };
  }
};

/**
 * Mark all alerts as read for a farm
 * @param farmId The ID of the farm to mark all alerts as read for
 * @returns Promise<{ message: string }> Success message
 */
export const markAllAlertsAsRead = async (farmId: string): Promise<{ message: string }> => {
  try {
    const response = await axios.put(`${API_URL}/alerts/read-all?farmId=${farmId}`);
    return response.data;
  } catch (error: unknown) {
    console.error('Error marking all alerts as read:', error);

    // Use the new error handler to get a structured error
    const structuredError = handleApiError(error, undefined, 'Failed to mark all alerts as read');

    // Log the structured error for debugging
    console.error('Structured error:', structuredError);

    // Return a fallback message instead of throwing an error
    return { message: 'Failed to mark all alerts as read. Please try again later.' };
  }
};

/**
 * Get alert rules for a farm
 * @param farmId The ID of the farm to get alert rules for
 * @returns Promise<AlertRule[]> Alert rules
 */
export const getAlertRules = async (farmId: string): Promise<AlertRule[]> => {
  try {
    const response = await axios.get(`${API_URL}/alerts/rules?farmId=${farmId}`);
    return response.data.rules;
  } catch (error: unknown) {
    console.error('Error fetching alert rules:', error);

    // Log detailed error information
    if (axios.isAxiosError(error) && error.response) {
      console.error('Response error data:', error.response.data);
      console.error('Response error status:', error.response.status);
    }

    // Return empty array instead of throwing an error
    return [];
  }
};

/**
 * Create a new alert rule
 * @param rule The alert rule to create
 * @returns Promise<AlertRule> The created alert rule
 */
export const createAlertRule = async (rule: Omit<AlertRule, 'id' | 'createdAt' | 'updatedAt'>): Promise<AlertRule> => {
  try {
    const response = await axios.post(`${API_URL}/alerts/rules`, rule);
    return response.data.rule;
  } catch (error: unknown) {
    console.error('Error creating alert rule:', error);

    // Log detailed error information
    if (axios.isAxiosError(error) && error.response) {
      console.error('Response error data:', error.response.data);
      console.error('Response error status:', error.response.status);
    }

    // Use the error handler to get a structured error
    const structuredError = handleApiError(error, undefined, 'Failed to create alert rule');
    console.error('Structured error:', structuredError);

    // Rethrow with more context since this is a creation operation that needs feedback
    throw new Error(structuredError.message);
  }
};

/**
 * Update an alert rule
 * @param ruleId The ID of the alert rule to update
 * @param rule The updated alert rule
 * @returns Promise<AlertRule> The updated alert rule
 */
export const updateAlertRule = async (ruleId: string, rule: Partial<AlertRule>): Promise<AlertRule> => {
  try {
    const response = await axios.put(`${API_URL}/alerts/rules/${ruleId}`, rule);
    return response.data.rule;
  } catch (error: unknown) {
    console.error('Error updating alert rule:', error);

    // Log detailed error information
    if (axios.isAxiosError(error) && error.response) {
      console.error('Response error data:', error.response.data);
      console.error('Response error status:', error.response.status);
    }

    // Use the error handler to get a structured error
    const structuredError = handleApiError(error, undefined, 'Failed to update alert rule');
    console.error('Structured error:', structuredError);

    // Rethrow with more context since this is an update operation that needs feedback
    throw new Error(structuredError.message);
  }
};

/**
 * Delete an alert rule
 * @param ruleId The ID of the alert rule to delete
 * @param farmId The ID of the farm the rule belongs to
 * @returns Promise<{ message: string }> Success message
 */
export const deleteAlertRule = async (ruleId: string, farmId: string): Promise<{ message: string }> => {
  try {
    const response = await axios.delete(`${API_URL}/alerts/rules/${ruleId}?farmId=${farmId}`);
    return response.data;
  } catch (error: unknown) {
    console.error('Error deleting alert rule:', error);

    // Log detailed error information
    if (axios.isAxiosError(error) && error.response) {
      console.error('Response error data:', error.response.data);
      console.error('Response error status:', error.response.status);
    }

    // Use the error handler to get a structured error
    const structuredError = handleApiError(error, undefined, 'Failed to delete alert rule');
    console.error('Structured error:', structuredError);

    // Return a fallback message instead of throwing an error
    return { message: 'Failed to delete alert rule. Please try again later.' };
  }
};

/**
 * Test an alert rule
 * @param rule The alert rule to test
 * @returns Promise<{ message: string; triggered: boolean }> Test result
 */
export const testAlertRule = async (
  rule: Omit<AlertRule, 'id' | 'createdAt' | 'updatedAt'>
): Promise<{ message: string; triggered: boolean }> => {
  try {
    const response = await axios.post(`${API_URL}/alerts/rules/test`, rule);
    return response.data;
  } catch (error: unknown) {
    console.error('Error testing alert rule:', error);

    // Log detailed error information
    if (axios.isAxiosError(error) && error.response) {
      console.error('Response error data:', error.response.data);
      console.error('Response error status:', error.response.status);
    }

    // Use the error handler to get a structured error
    const structuredError = handleApiError(error, undefined, 'Failed to test alert rule');
    console.error('Structured error:', structuredError);

    // Return a fallback result instead of throwing an error
    return { 
      message: 'Failed to test alert rule. Please try again later.',
      triggered: false
    };
  }
};
