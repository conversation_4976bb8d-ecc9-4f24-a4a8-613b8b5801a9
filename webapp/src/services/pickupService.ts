import axios from 'axios';
import { API_URL } from '../config';
import { handleApiError } from '../utils/errorHandler';
import { getAuthToken } from '../utils/storageUtils';

// Define Pickup interface
export interface Pickup {
  id: string;
  pickupDate: string;
  status: string;
  location: string;
  contactName: string;
  contactPhone: string;
  driverId: string;
  driverName: string;
  pickupType: string;
  items: Array<{
    id: string;
    name: string;
    quantity: number;
    unit: string;
  }>;
  notes: string;
  referenceNumber: string;
  estimatedCompletion: string;
  farmId: string;
  tenantId: string;
  createdAt: string;
  updatedAt: string;
}

// Get all pickups for a farm
export const getPickups = async (
  farmId: string,
  params: {
    search?: string;
    status?: string;
    startDate?: string;
    endDate?: string;
    driverId?: string;
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  } = {}
) => {
  try {
    const token = getAuthToken();

    // Build query string
    const queryParams = new URLSearchParams();
    queryParams.append('farmId', farmId);

    if (params.search) queryParams.append('search', params.search);
    if (params.status) queryParams.append('status', params.status);
    if (params.startDate) queryParams.append('startDate', params.startDate);
    if (params.endDate) queryParams.append('endDate', params.endDate);
    if (params.driverId) queryParams.append('driverId', params.driverId);
    if (params.page) queryParams.append('page', params.page.toString());
    if (params.limit) queryParams.append('limit', params.limit.toString());
    if (params.sortBy) queryParams.append('sortBy', params.sortBy);
    if (params.sortOrder) queryParams.append('sortOrder', params.sortOrder);

    const queryString = queryParams.toString();
    const url = `${API_URL}/pickups${queryString ? `?${queryString}` : ''}`;

    const response = await axios.get(url, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    return Array.isArray(response.data) ? response.data : response.data.pickups || [];
  } catch (error: unknown) {
    console.error('Error fetching pickups:', error);

    // Use the error handler to get a structured error
    const structuredError = handleApiError(error, undefined, 'Failed to fetch pickups');

    // Create a custom error with the structured information
    const customError = new Error(structuredError.message);
    (customError as any).structuredError = structuredError;

    throw customError;
  }
};

// Get pickup by ID
export const getPickupById = async (id: string) => {
  try {
    const token = getAuthToken();

    const response = await axios.get(`${API_URL}/pickups/${id}`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    return response.data;
  } catch (error: unknown) {
    console.error('Error fetching pickup:', error);

    // Use the error handler to get a structured error
    const structuredError = handleApiError(error, undefined, 'Failed to fetch pickup details');

    // Create a custom error with the structured information
    const customError = new Error(structuredError.message);
    (customError as any).structuredError = structuredError;

    throw customError;
  }
};

// Create a new pickup
export const createPickup = async (
  data: {
    pickupDate: string;
    status: string;
    location: string;
    contactName: string;
    contactPhone?: string;
    driverId?: string;
    pickupType?: string;
    items?: Array<{
      id: string;
      name: string;
      quantity: number;
      unit: string;
    }>;
    notes?: string;
    referenceNumber?: string;
    estimatedCompletion?: string;
    farmId: string;
  }
) => {
  try {
    const token = getAuthToken();

    const response = await axios.post(`${API_URL}/pickups`, data, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    return response.data;
  } catch (error: unknown) {
    console.error('Error creating pickup:', error);

    // Use the error handler to get a structured error
    const structuredError = handleApiError(error, undefined, 'Failed to create pickup');

    // Create a custom error with the structured information
    const customError = new Error(structuredError.message);
    (customError as any).structuredError = structuredError;

    throw customError;
  }
};

// Update a pickup
export const updatePickup = async (
  id: string,
  data: {
    pickupDate?: string;
    status?: string;
    location?: string;
    contactName?: string;
    contactPhone?: string;
    driverId?: string;
    pickupType?: string;
    items?: Array<{
      id: string;
      name: string;
      quantity: number;
      unit: string;
    }>;
    notes?: string;
    referenceNumber?: string;
    estimatedCompletion?: string;
  }
) => {
  try {
    const token = getAuthToken();

    const response = await axios.put(`${API_URL}/pickups/${id}`, data, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    return response.data;
  } catch (error: unknown) {
    console.error('Error updating pickup:', error);

    // Use the error handler to get a structured error
    const structuredError = handleApiError(error, undefined, 'Failed to update pickup');

    // Create a custom error with the structured information
    const customError = new Error(structuredError.message);
    (customError as any).structuredError = structuredError;

    throw customError;
  }
};

// Delete a pickup
export const deletePickup = async (id: string) => {
  try {
    const token = getAuthToken();

    const response = await axios.delete(`${API_URL}/pickups/${id}`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    return response.data;
  } catch (error: unknown) {
    console.error('Error deleting pickup:', error);

    // Use the error handler to get a structured error
    const structuredError = handleApiError(error, undefined, 'Failed to delete pickup');

    // Create a custom error with the structured information
    const customError = new Error(structuredError.message);
    (customError as any).structuredError = structuredError;

    throw customError;
  }
};

// Update pickup status
export const updatePickupStatus = async (
  id: string,
  status: string
) => {
  try {
    const token = getAuthToken();

    const response = await axios.patch(`${API_URL}/pickups/${id}/status`, { status }, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    return response.data;
  } catch (error: unknown) {
    console.error('Error updating pickup status:', error);

    // Use the error handler to get a structured error
    const structuredError = handleApiError(error, undefined, 'Failed to update pickup status');

    // Create a custom error with the structured information
    const customError = new Error(structuredError.message);
    (customError as any).structuredError = structuredError;

    throw customError;
  }
};

// Format date for display
export const formatDate = (dateString: string | null) => {
  if (!dateString) return 'N/A';
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};
