/* Custom styles for GridStack */
.grid-stack {
  background: #f9fafb;
  border-radius: 0.5rem;
  padding: 0.5rem;
  overflow: visible !important;
  /* Using margin instead of gap for GridStack compatibility */
  /* The actual spacing is controlled by GridStack's margin property */
  /* Override any inline styles that might be applied */
  width: 100% !important;
  height: auto !important;
}

.dark .grid-stack {
  background: #1f2937; /* Dark background for dark mode */
}

@media (min-width: 640px) {
  .grid-stack {
    padding: 1rem;
  }
}

.grid-stack-item {
  border-radius: 0.5rem;
  overflow: visible;
  /* Spacing between items is controlled by GridStack's margin property */
  /* Auto height is enabled to adjust based on content */
  height: auto !important;
  /* Override any inline styles that might be applied */
  position: relative !important;
  width: auto !important;
  box-sizing: border-box !important;
}

.grid-stack-item-content {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  overflow: visible; /* Allow content to determine height */
  display: flex;
  flex-direction: column;
  min-height: 100%;
  height: auto !important; /* Force height to adjust based on content */
  /* Ensure content is properly sized for GridStack's autoHeight */
  position: relative !important;
  width: 100% !important;
  left: 0 !important;
  right: 0 !important;
  top: 0 !important;
  bottom: 0 !important;
  box-sizing: border-box !important;
}

.dark .grid-stack-item-content {
  background: #374151; /* Dark background for dark mode */
  color: #f3f4f6; /* Light text for dark mode */
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(0, 0, 0, 0.2); /* Darker shadow for dark mode */
}

/* Simplified hover effect to reduce flickering */
.grid-stack:not(.grid-stack-animate) .grid-stack-item-content:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.dark .grid-stack:not(.grid-stack-animate) .grid-stack-item-content:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2); /* Darker shadow for dark mode hover */
}

.widget-content {
  height: auto !important;
  overflow: visible;
  padding: 0.5rem;
  box-sizing: border-box; /* Ensure padding is included in the element's dimensions */
  width: 100%;
  display: flex;
  flex-direction: column;
}

.widget-content-container {
  overflow: visible; /* Allow content to determine height */
  padding: 0;
  flex: 1;
  max-height: none; /* Remove max-height constraint to allow content to determine height */
  width: 100%;
  display: flex;
  flex-direction: column;
}

.widget-wrapper {
  width: 100%;
  height: auto !important;
  min-height: 50px;
  display: flex;
  flex-direction: column;
  flex: 1;
}

@media (min-width: 640px) {
  .widget-content {
    padding: 0.75rem;
  }
}

/* Resize handle styles */
.ui-resizable-handle {
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

.dark .ui-resizable-handle {
  background-color: rgba(255, 255, 255, 0.2); /* Lighter background for dark mode */
}

.ui-resizable-handle:hover {
  background-color: rgba(0, 0, 0, 0.2);
}

.dark .ui-resizable-handle:hover {
  background-color: rgba(255, 255, 255, 0.3); /* Lighter background for dark mode hover */
}

.ui-resizable-se {
  width: 16px;
  height: 16px;
  right: 5px;
  bottom: 5px;
}

@media (max-width: 640px) {
  .ui-resizable-handle {
    width: 20px !important;
    height: 20px !important;
  }
}

/* Style for when in edit mode */
.grid-stack:not(.grid-stack-static) .grid-stack-item:hover {
  border: 1px dashed #082f49;
}

.dark .grid-stack:not(.grid-stack-static) .grid-stack-item:hover {
  border: 1px dashed #93c5fd; /* Lighter border color for dark mode */
}

/* Animation for widget changes - improved for smoother experience */
.grid-stack-item.ui-draggable-dragging,
.grid-stack-item.ui-resizable-resizing,
.grid-stack-item.widget-dragging {
  opacity: 0.9; /* Increased opacity for better visibility */
  z-index: 1000; /* Higher z-index to ensure it's above other elements */
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  cursor: grabbing;
  transition: none !important; /* Disable transitions during drag/resize */
  will-change: transform; /* Optimize for animations */
}

.dark .grid-stack-item.ui-draggable-dragging,
.dark .grid-stack-item.ui-resizable-resizing,
.dark .grid-stack-item.widget-dragging {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2); /* Darker shadow for dark mode */
}

/* Placeholder style during drag */
.grid-stack .grid-stack-placeholder > .placeholder-content {
  background-color: rgba(79, 70, 229, 0.2); /* Slightly more visible */
  border: 2px dashed #082f49; /* Thicker border for better visibility */
  border-radius: 0.5rem;
}

.dark .grid-stack .grid-stack-placeholder > .placeholder-content {
  background-color: rgba(99, 102, 241, 0.3); /* More visible in dark mode */
  border: 2px dashed #93c5fd; /* Lighter border color for dark mode */
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .grid-stack-item {
    /* Removed min-height constraint to allow auto height to work properly */
  }
}
