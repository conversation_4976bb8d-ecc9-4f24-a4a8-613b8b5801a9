
import 'vite/modulepreload-polyfill'
import { createRoot } from 'react-dom/client'
import './index.css'
import App from './App.tsx'
import * as Sentry from "@sentry/react";
import { HeadProvider } from 'react-head';
import ThemeWrapper from './components/theme/ThemeWrapper';
import { initializeReactDebugging } from './utils/debugReactErrors';
// Import the feedback integration separately

Sentry.init({
    dsn: "https://<EMAIL>/4509238976577536",
    // Setting this option to true will send default PII data to Sentry.
    // For example, automatic IP address collection on events
    sendDefaultPii: true,
    integrations: [
        Sentry.feedbackIntegration({
            colorScheme: "system",

        }),
    ]
});

// Initialize React debugging utilities in development
initializeReactDebugging();

const container = document.getElementById("root");
const root = createRoot(container);
root.render(
  <HeadProvider>
    <ThemeWrapper>
      <App />
    </ThemeWrapper>
  </HeadProvider>
);
