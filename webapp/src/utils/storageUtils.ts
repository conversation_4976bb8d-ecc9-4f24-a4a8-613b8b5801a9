/**
 * Utility functions for handling storage across subdomains
 * This utility allows data to be stored in both localStorage and cookies
 * so that it can be accessed across subdomains
 */

// Get the main domain from the current hostname
// e.g., if hostname is "farm1.nxtacre.com", mainDomain will be "nxtacre.com"
const getMainDomain = (): string => {
  const hostname = window.location.hostname;

  // If localhost or IP address, return as is
  if (hostname === 'localhost' || /^(\d{1,3}\.){3}\d{1,3}$/.test(hostname)) {
    return hostname;
  }

  // Extract the main domain (last two parts of the hostname)
  const parts = hostname.split('.');
  if (parts.length >= 2) {
    return parts.slice(-2).join('.');
  }

  return hostname;
};

// Set a value in both localStorage and cookies
export const setStorageItem = (key: string, value: string): void => {
  try {
    // Store in localStorage for backward compatibility
    localStorage.setItem(key, value);

    // For token, use auth_token as the cookie name to be consistent with server-side
    const cookieKey = key === 'token' ? 'auth_token' : key;

    // Also store in a cookie that's accessible across subdomains
    const mainDomain = getMainDomain();
    const expires = new Date();
    expires.setDate(expires.getDate() + 7); // Cookie expires in 7 days
    const isSecure = window.location.protocol === 'https:';
    const secureFlag = isSecure ? '; secure' : '';

    document.cookie = `${cookieKey}=${encodeURIComponent(value)}; expires=${expires.toUTCString()}; path=/; domain=.${mainDomain}; samesite=lax${secureFlag}`;
  } catch (error) {
    console.error('Error setting storage item:', error);
  }
};

// Get a value from localStorage or cookies
export const getStorageItem = (key: string): string | null => {
  try {
    // For auth_token, prioritize cookies over localStorage
    if (key === 'auth_token') {
      // Try to get from cookies first
      const cookieValue = getCookieValue(key);
      if (cookieValue) {
        return cookieValue;
      }

      // If not in cookies, try localStorage
      return localStorage.getItem(key);
    }

    // For other keys, try localStorage first (for backward compatibility)
    const localValue = localStorage.getItem(key);
    if (localValue) {
      return localValue;
    }

    // If not in localStorage, try to get from cookies
    return getCookieValue(key);
  } catch (error) {
    console.error('Error getting storage item:', error);
    return null;
  }
};

// Remove a value from both localStorage and cookies
export const removeStorageItem = (key: string): void => {
  try {
    // Remove from localStorage
    localStorage.removeItem(key);

    // Remove from cookies
    const mainDomain = getMainDomain();
    const isSecure = window.location.protocol === 'https:';
    const secureFlag = isSecure ? '; secure' : '';

    // For token, also remove auth_token cookie to be consistent with server-side
    const cookieKey = key === 'token' ? 'auth_token' : key;
    document.cookie = `${cookieKey}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.${mainDomain}; samesite=lax${secureFlag}`;

    // If removing token, also remove the original key to ensure both are cleared
    if (key === 'token') {
      document.cookie = `${key}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.${mainDomain}; samesite=lax${secureFlag}`;
    }
  } catch (error) {
    console.error('Error removing storage item:', error);
  }
};

// Set a JSON value in storage
export const setStorageJSON = (key: string, value: any): void => {
  try {
    const jsonValue = JSON.stringify(value);
    setStorageItem(key, jsonValue);
  } catch (error) {
    console.error('Error setting JSON in storage:', error);
  }
};

// Get a JSON value from storage
export const getStorageJSON = <T>(key: string): T | null => {
  try {
    const value = getStorageItem(key);
    if (value) {
      return JSON.parse(value) as T;
    }
    return null;
  } catch (error) {
    console.error('Error getting JSON from storage:', error);
    return null;
  }
};

// Helper function to get a value directly from cookies
export const getCookieValue = (key: string): string | null => {
  try {
    const cookies = document.cookie.split(';');
    for (let i = 0; i < cookies.length; i++) {
      const cookie = cookies[i].trim();
      if (cookie.startsWith(`${key}=`)) {
        return decodeURIComponent(cookie.substring(key.length + 1));
      }
    }
    return null;
  } catch (error) {
    console.error('Error getting cookie value:', error);
    return null;
  }
};

// Convenience function to get the authentication token
export const getAuthToken = (): string | null => {
  // First try to get from auth_token cookie (server-set)
  let token = getCookieValue('auth_token');

  // If not found in cookies, fall back to localStorage for backward compatibility
  if (!token) {
    token = localStorage.getItem('token');
  }

  return token;
};

// Convenience function to get the user object
export const getUser = <T>(): T | null => {
  return getStorageJSON<T>('user');
};

// Convenience function to get the farm subdomain
export const getFarmSubdomain = (): string | null => {
  return getStorageItem('farmSubdomain');
};

// Convenience function to get the farm ID
export const getFarmId = (): string | null => {
  return getStorageItem('farmId');
};

// Clear all authentication-related storage items and cookies
export const clearAuthStorage = (): void => {
  // List of all authentication-related keys
  const authKeys = [
    'token',
    'auth_token',
    'refresh_token',
    'refreshToken',
    'user',
    'farmSubdomain',
    'farmId',
    'impersonating',
    'admin_id',
    'lastActivity'
  ];

  // Remove each key from both localStorage and cookies
  authKeys.forEach(key => {
    removeStorageItem(key);
  });

  // Ensure both token and auth_token cookies are removed directly
  const mainDomain = getMainDomain();
  const isSecure = window.location.protocol === 'https:';
  const secureFlag = isSecure ? '; secure' : '';

  document.cookie = `token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.${mainDomain}; samesite=lax${secureFlag}`;
  document.cookie = `auth_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.${mainDomain}; samesite=lax${secureFlag}`;
};
