import React, { lazy, Suspense } from 'react';
import { Routes, Route } from 'react-router-dom';

// Lazy load components
const CropDiseasePrediction = lazy(() => import('./CropDiseasePrediction'));
const YieldPrediction = lazy(() => import('./YieldPrediction'));
const CropRotationOptimization = lazy(() => import('./CropRotationOptimization'));
const HarvestScheduling = lazy(() => import('./HarvestScheduling'));
const CropTypeManagement = lazy(() => import('./CropTypeManagement'));

// Loading component
const ComponentLoader = () => (
  <div className="flex items-center justify-center h-64">
    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
    <p className="ml-3 text-gray-500">Loading...</p>
  </div>
);

// Default export for the main routes
const CropManagementRoutes = () => {
  return (
    <Routes>
      <Route path="/" element={
        <Suspense fallback={<ComponentLoader />}>
          <CropDiseasePrediction />
        </Suspense>
      } />
      <Route path="/disease-prediction" element={
        <Suspense fallback={<ComponentLoader />}>
          <CropDiseasePrediction />
        </Suspense>
      } />
      <Route path="/yield-prediction" element={
        <Suspense fallback={<ComponentLoader />}>
          <YieldPrediction />
        </Suspense>
      } />
      <Route path="/rotation-optimization" element={
        <Suspense fallback={<ComponentLoader />}>
          <CropRotationOptimization />
        </Suspense>
      } />
      <Route path="/harvest-scheduling" element={
        <Suspense fallback={<ComponentLoader />}>
          <HarvestScheduling />
        </Suspense>
      } />
      <Route path="/crop-types" element={
        <Suspense fallback={<ComponentLoader />}>
          <CropTypeManagement />
        </Suspense>
      } />
    </Routes>
  );
};

export default CropManagementRoutes;
