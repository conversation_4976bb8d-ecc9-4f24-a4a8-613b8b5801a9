import { useState, useEffect, useContext, useCallback, useRef } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import axios from 'axios';
import { AuthContext } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import Layout from '../../components/Layout';
import ContextMenu from '../../components/ContextMenu';
import ErrorBoundary from '../../components/ui/ErrorBoundary';
import { API_URL } from '../../config';
import { getAuthToken } from '../../utils/storageUtils';
import { Document as PDFDocument, Page } from 'react-pdf';
import { logReactError } from '../../utils/reactErrorDecoder';
import 'react-pdf/dist/esm/Page/AnnotationLayer.css';

interface Document {
  id: string;
  name: string;
  description: string;
  file_path: string;
  file_size: number;
  file_type: string;
  mime_type: string;
  is_external: boolean;
  external_source: string | null;
  folder_id: string | null;
  farm_id: string;
  uploaded_by: string;
  created_at: string;
  updated_at: string;
  uploader: {
    id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
  folder?: {
    id: string;
    name: string;
  };
}

interface Folder {
  id: string;
  name: string;
  description: string;
  parent_folder_id: string | null;
  farm_id: string;
  created_by: string;
  created_at: string;
  updated_at: string;
  creator?: {
    id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
}

const DocumentList = () => {
  const [documents, setDocuments] = useState<Document[]>([]);
  const [subfolders, setSubfolders] = useState<Folder[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentFolder, setCurrentFolder] = useState<string | null>(null);
  const [folderPath, setFolderPath] = useState<{ id: string | null; name: string }[]>([
    { id: null, name: 'Root' }
  ]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // Selection state
  const [selectedItems, setSelectedItems] = useState<{
    documents: string[];
    folders: string[];
  }>({ documents: [], folders: [] });
  const [selectionStartPoint, setSelectionStartPoint] = useState<{ x: number; y: number } | null>(null);
  const [selectionBox, setSelectionBox] = useState<{
    left: number;
    top: number;
    width: number;
    height: number;
  } | null>(null);
  const [isSelecting, setIsSelecting] = useState(false);
  const [lastSelectedItem, setLastSelectedItem] = useState<{ type: 'document' | 'folder'; id: string } | null>(null);

  // File viewer/editor state
  const [viewerOpen, setViewerOpen] = useState(false);
  const [editorOpen, setEditorOpen] = useState(false);
  const [fileContent, setFileContent] = useState<string>('');
  const [fileType, setFileType] = useState<string>('');
  const [fileName, setFileName] = useState<string>('');
  const [numPages, setNumPages] = useState<number | null>(null);
  const [pageNumber, setPageNumber] = useState<number>(1);

  // Create file/folder modal state
  const [createModalOpen, setCreateModalOpen] = useState(false);
  const [createType, setCreateType] = useState<'file' | 'folder'>('file');
  const [newItemName, setNewItemName] = useState('');
  const [newItemDescription, setNewItemDescription] = useState('');

  // Refs
  const explorerRef = useRef<HTMLDivElement>(null);
  const documentListRef = useRef<HTMLDivElement>(null);
  const successMessageTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  const { user } = useContext(AuthContext);
  const { currentFarm } = useFarm();
  const navigate = useNavigate();

  // Cleanup timeouts on unmount
  useEffect(() => {
    return () => {
      if (successMessageTimeoutRef.current) {
        clearTimeout(successMessageTimeoutRef.current);
        successMessageTimeoutRef.current = null;
      }
    };
  }, []);

  // Fetch documents and subfolders
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError(null);

      try {
        // Fetch documents
        let documentsUrl = `${API_URL}/documents/farm/${currentFarm?.id || user?.farm_id}/documents`;
        const params: Record<string, string> = {};

        if (currentFolder) {
          params.folderId = currentFolder;
        } else if (currentFolder === null) {
          params.folderId = 'null'; // Explicitly request root documents
        }

        if (searchQuery) {
          params.search = searchQuery;
        }

        // Add query parameters if any filters are applied
        if (Object.keys(params).length > 0) {
          const queryString = new URLSearchParams(params).toString();
          documentsUrl = `${documentsUrl}?${queryString}`;
        }

        console.log('Fetching documents from:', documentsUrl);
        const documentsResponse = await axios.get(documentsUrl, {
          headers: {
            Authorization: `Bearer ${getAuthToken()}`
          }
        });

        console.log('Documents response:', documentsResponse.data);
        setDocuments(documentsResponse.data.documents || []);

        // Fetch subfolders
        let foldersUrl = `${API_URL}/documents/farm/${currentFarm?.id || user?.farm_id}/folders`;
        const folderParams: Record<string, string> = {};

        if (currentFolder) {
          folderParams.parentFolderId = currentFolder;
        } else if (currentFolder === null) {
          folderParams.parentFolderId = 'null'; // Explicitly request root folders
        }

        if (searchQuery) {
          folderParams.search = searchQuery;
        }

        // Add query parameters if any filters are applied
        if (Object.keys(folderParams).length > 0) {
          const queryString = new URLSearchParams(folderParams).toString();
          foldersUrl = `${foldersUrl}?${queryString}`;
        }

        console.log('Fetching folders from:', foldersUrl);
        const foldersResponse = await axios.get(foldersUrl, {
          headers: {
            Authorization: `Bearer ${getAuthToken()}`
          }
        });

        console.log('Folders response:', foldersResponse.data);
        setSubfolders(foldersResponse.data || []);
      } catch (err: any) {
        console.error('Error fetching data:', err);
        const errorMessage = err.response?.data?.error || 'Failed to load documents and folders. Please try again later.';
        setError(errorMessage);
      } finally {
        setLoading(false);
      }
    };

    if (currentFarm?.id || user?.farm_id) {
      fetchData();
    } else {
      setError('No farm selected. Please select a farm to view documents.');
      setLoading(false);
    }
  }, [currentFarm?.id, user?.farm_id, currentFolder, searchQuery]);

  // Format file size for display
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
  };

  // Handle document actions
  const handleViewDocument = useCallback((document: Document) => {
    navigate(`/documents/view/${document.id}`);
  }, [navigate]);

  const handleDownloadDocument = useCallback((document: Document) => {
    const token = getAuthToken();
    window.open(`${API_URL}/documents/${document.id}/download?token=${token}`, '_blank');
  }, []);

  const handleEditDocument = useCallback((document: Document) => {
    navigate(`/documents/edit/${document.id}`);
  }, [navigate]);

  const handleDeleteDocument = useCallback(async (document: Document) => {
    try {
      await axios.delete(`${API_URL}/documents/${document.id}`, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });

      // Remove document from state
      setDocuments(prev => prev.filter(d => d.id !== document.id));

      // Show success message
      setSuccessMessage('Document deleted successfully');

      // Hide success message after 3 seconds
      if (successMessageTimeoutRef.current) {
        clearTimeout(successMessageTimeoutRef.current);
      }
      successMessageTimeoutRef.current = setTimeout(() => {
        setSuccessMessage(null);
        successMessageTimeoutRef.current = null;
      }, 3000);
    } catch (err: any) {
      console.error('Error deleting document:', err);
      setError(err.response?.data?.error || 'Failed to delete document. Please try again later.');
    }
  }, []);

  const handleImportDocument = useCallback(async (document: Document) => {
    if (!document.is_external) return;

    try {
      setLoading(true);
      await axios.post(
        `${API_URL}/documents/farm/${currentFarm?.id || user?.farm_id}/documents/${document.id}/import`,
        {},
        {
          headers: {
            Authorization: `Bearer ${getAuthToken()}`
          }
        }
      );

      // Refresh document list
      const fetchData = async () => {
        try {
          // Fetch documents
          let documentsUrl = `${API_URL}/documents/farm/${currentFarm?.id || user?.farm_id}/documents`;
          const params: Record<string, string> = {};

          if (currentFolder) {
            params.folderId = currentFolder;
          } else if (currentFolder === null) {
            params.folderId = 'null'; // Explicitly request root documents
          }

          if (searchQuery) {
            params.search = searchQuery;
          }

          // Add query parameters if any filters are applied
          if (Object.keys(params).length > 0) {
            const queryString = new URLSearchParams(params).toString();
            documentsUrl = `${documentsUrl}?${queryString}`;
          }

          const documentsResponse = await axios.get(documentsUrl, {
            headers: {
              Authorization: `Bearer ${getAuthToken()}`
            }
          });

          setDocuments(documentsResponse.data.documents || []);
        } catch (err: any) {
          console.error('Error refreshing documents:', err);
          setError(err.response?.data?.error || 'Failed to refresh documents. Please try again later.');
        } finally {
          setLoading(false);
        }
      };

      await fetchData();

      // Show success message
      setSuccessMessage('Document imported successfully');

      // Hide success message after 3 seconds
      if (successMessageTimeoutRef.current) {
        clearTimeout(successMessageTimeoutRef.current);
      }
      successMessageTimeoutRef.current = setTimeout(() => {
        setSuccessMessage(null);
        successMessageTimeoutRef.current = null;
      }, 3000);
    } catch (err: any) {
      console.error('Error importing document:', err);
      setError(err.response?.data?.error || 'Failed to import document. Please try again later.');
      setLoading(false);
    }
  }, [currentFolder, currentFarm?.id, user?.farm_id, searchQuery]);

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  // Handle folder navigation
  const navigateToFolder = (folderId: string | null, folderName: string) => {
    setCurrentFolder(folderId);
    // Clear selection when navigating
    setSelectedItems({ documents: [], folders: [] });

    if (folderId === null) {
      // Reset to root
      setFolderPath([{ id: null, name: 'Root' }]);
    } else {
      // Add to path
      setFolderPath(prev => {
        // Check if we're navigating to a folder that's already in the path
        const existingIndex = prev.findIndex(item => item.id === folderId);
        if (existingIndex >= 0) {
          // If so, truncate the path to that point
          return prev.slice(0, existingIndex + 1);
        }
        // Otherwise, add the new folder to the path
        return [...prev, { id: folderId, name: folderName }];
      });
    }
  };

  // Selection functionality
  const handleMouseDown = (e: React.MouseEvent) => {
    // Only start selection on left click and if the click is on the explorer area (not on a file/folder)
    if (e.button !== 0 || !(e.target as HTMLElement).classList.contains('explorer-area')) return;

    // Get the explorer element's position
    const explorerRect = explorerRef.current?.getBoundingClientRect();
    if (!explorerRect) return;

    // Calculate the click position relative to the explorer
    const x = e.clientX - explorerRect.left;
    const y = e.clientY - explorerRect.top;

    setSelectionStartPoint({ x, y });
    setSelectionBox({ left: x, top: y, width: 0, height: 0 });
    setIsSelecting(true);

    // Clear selection if not holding shift or cmd/ctrl
    if (!e.shiftKey && !e.metaKey && !e.ctrlKey) {
      setSelectedItems({ documents: [], folders: [] });
      setLastSelectedItem(null);
    }
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isSelecting || !selectionStartPoint) return;

    // Get the explorer element's position
    const explorerRect = explorerRef.current?.getBoundingClientRect();
    if (!explorerRect) return;

    // Calculate the current position relative to the explorer
    const x = e.clientX - explorerRect.left;
    const y = e.clientY - explorerRect.top;

    // Calculate the selection box dimensions
    const left = Math.min(selectionStartPoint.x, x);
    const top = Math.min(selectionStartPoint.y, y);
    const width = Math.abs(x - selectionStartPoint.x);
    const height = Math.abs(y - selectionStartPoint.y);

    setSelectionBox({ left, top, width, height });

    // Check which items are within the selection box
    updateSelectedItemsFromBox({ left, top, width, height });
  };

  const handleMouseUp = () => {
    setIsSelecting(false);
    setSelectionStartPoint(null);
    setSelectionBox(null);
  };

  const updateSelectedItemsFromBox = (box: { left: number; top: number; width: number; height: number }) => {
    if (!explorerRef.current) return;

    const selectedDocIds: string[] = [];
    const selectedFolderIds: string[] = [];

    // Check folders
    const folderElements = explorerRef.current.querySelectorAll('.folder-item');
    folderElements.forEach((element) => {
      const rect = element.getBoundingClientRect();
      const explorerRect = explorerRef.current!.getBoundingClientRect();

      // Convert to explorer-relative coordinates
      const relRect = {
        left: rect.left - explorerRect.left,
        top: rect.top - explorerRect.top,
        right: rect.right - explorerRect.left,
        bottom: rect.bottom - explorerRect.top
      };

      // Check if the element intersects with the selection box
      if (
        relRect.left < box.left + box.width &&
        relRect.right > box.left &&
        relRect.top < box.top + box.height &&
        relRect.bottom > box.top
      ) {
        const folderId = element.getAttribute('data-id');
        if (folderId) selectedFolderIds.push(folderId);
      }
    });

    // Check documents
    const docElements = explorerRef.current.querySelectorAll('.document-item');
    docElements.forEach((element) => {
      const rect = element.getBoundingClientRect();
      const explorerRect = explorerRef.current!.getBoundingClientRect();

      // Convert to explorer-relative coordinates
      const relRect = {
        left: rect.left - explorerRect.left,
        top: rect.top - explorerRect.top,
        right: rect.right - explorerRect.left,
        bottom: rect.bottom - explorerRect.top
      };

      // Check if the element intersects with the selection box
      if (
        relRect.left < box.left + box.width &&
        relRect.right > box.left &&
        relRect.top < box.top + box.height &&
        relRect.bottom > box.top
      ) {
        const docId = element.getAttribute('data-id');
        if (docId) selectedDocIds.push(docId);
      }
    });

    setSelectedItems(prev => ({
      documents: [...new Set([...prev.documents, ...selectedDocIds])],
      folders: [...new Set([...prev.folders, ...selectedFolderIds])]
    }));
  };

  const handleItemClick = (e: React.MouseEvent, type: 'document' | 'folder', id: string) => {
    e.stopPropagation();

    if (e.shiftKey && lastSelectedItem) {
      // Shift+click: select range
      const items = type === 'document' ? documents : subfolders;
      const ids = items.map(item => item.id);
      const currentIndex = ids.indexOf(id);
      const lastIndex = ids.indexOf(lastSelectedItem.type === type ? lastSelectedItem.id : ids[0]);

      const start = Math.min(currentIndex, lastIndex);
      const end = Math.max(currentIndex, lastIndex);
      const rangeIds = ids.slice(start, end + 1);

      if (type === 'document') {
        setSelectedItems(prev => ({
          ...prev,
          documents: [...new Set([...prev.documents, ...rangeIds])]
        }));
      } else {
        setSelectedItems(prev => ({
          ...prev,
          folders: [...new Set([...prev.folders, ...rangeIds])]
        }));
      }
    } else if (e.metaKey || e.ctrlKey) {
      // Cmd/Ctrl+click: toggle selection
      setSelectedItems(prev => {
        if (type === 'document') {
          const newDocs = prev.documents.includes(id)
            ? prev.documents.filter(docId => docId !== id)
            : [...prev.documents, id];
          return { ...prev, documents: newDocs };
        } else {
          const newFolders = prev.folders.includes(id)
            ? prev.folders.filter(folderId => folderId !== id)
            : [...prev.folders, id];
          return { ...prev, folders: newFolders };
        }
      });
    } else {
      // Regular click: select only this item
      setSelectedItems({
        documents: type === 'document' ? [id] : [],
        folders: type === 'folder' ? [id] : []
      });
    }

    setLastSelectedItem({ type, id });
  };

  // Handle right-click on explorer area
  const handleExplorerContextMenu = (e: React.MouseEvent) => {
    // Only show context menu if right-clicking on the explorer area (not on a file/folder)
    if (!(e.target as HTMLElement).classList.contains('explorer-area')) return;

    e.preventDefault();
    // Context menu will be handled by the ContextMenu component
  };

  // Handle file/folder creation
  const handleCreateItem = async () => {
    try {
      if (createType === 'folder') {
        // Create folder
        await axios.post(
          `${API_URL}/documents/farm/${currentFarm?.id || user?.farm_id}/folders`,
          {
            name: newItemName,
            description: newItemDescription,
            parentFolderId: currentFolder
          },
          {
            headers: {
              Authorization: `Bearer ${getAuthToken()}`
            }
          }
        );

        setSuccessMessage('Folder created successfully');
      } else {
        // Create file (text document)
        const blob = new Blob([fileContent], { type: 'text/plain' });
        const file = new File([blob], newItemName, { type: 'text/plain' });

        const formData = new FormData();
        formData.append('file', file);
        formData.append('description', newItemDescription);
        if (currentFolder) {
          formData.append('folderId', currentFolder);
        }

        await axios.post(
          `${API_URL}/documents/farm/${currentFarm?.id || user?.farm_id}/documents`,
          formData,
          {
            headers: {
              Authorization: `Bearer ${getAuthToken()}`,
              'Content-Type': 'multipart/form-data'
            }
          }
        );

        setSuccessMessage('File created successfully');
      }

      // Refresh the document list
      const fetchData = async () => {
        try {
          // Fetch documents
          let documentsUrl = `${API_URL}/documents/farm/${currentFarm?.id || user?.farm_id}/documents`;
          const params: Record<string, string> = {};

          if (currentFolder) {
            params.folderId = currentFolder;
          } else if (currentFolder === null) {
            params.folderId = 'null'; // Explicitly request root documents
          }

          if (searchQuery) {
            params.search = searchQuery;
          }

          // Add query parameters if any filters are applied
          if (Object.keys(params).length > 0) {
            const queryString = new URLSearchParams(params).toString();
            documentsUrl = `${documentsUrl}?${queryString}`;
          }

          const documentsResponse = await axios.get(documentsUrl, {
            headers: {
              Authorization: `Bearer ${getAuthToken()}`
            }
          });

          setDocuments(documentsResponse.data.documents || []);

          // Fetch subfolders
          let foldersUrl = `${API_URL}/documents/farm/${currentFarm?.id || user?.farm_id}/folders`;
          const folderParams: Record<string, string> = {};

          if (currentFolder) {
            folderParams.parentFolderId = currentFolder;
          } else if (currentFolder === null) {
            folderParams.parentFolderId = 'null'; // Explicitly request root folders
          }

          if (searchQuery) {
            folderParams.search = searchQuery;
          }

          // Add query parameters if any filters are applied
          if (Object.keys(folderParams).length > 0) {
            const queryString = new URLSearchParams(folderParams).toString();
            foldersUrl = `${foldersUrl}?${queryString}`;
          }

          const foldersResponse = await axios.get(foldersUrl, {
            headers: {
              Authorization: `Bearer ${getAuthToken()}`
            }
          });

          setSubfolders(foldersResponse.data || []);
        } catch (err: any) {
          console.error('Error refreshing data:', err);
          setError(err.response?.data?.error || 'Failed to refresh data. Please try again later.');
        }
      };

      await fetchData();

      // Reset form and close modal
      setNewItemName('');
      setNewItemDescription('');
      setFileContent('');
      setCreateModalOpen(false);

      // Hide success message after 3 seconds
      if (successMessageTimeoutRef.current) {
        clearTimeout(successMessageTimeoutRef.current);
      }
      successMessageTimeoutRef.current = setTimeout(() => {
        setSuccessMessage(null);
        successMessageTimeoutRef.current = null;
      }, 3000);
    } catch (err: any) {
      console.error('Error creating item:', err);
      setError(err.response?.data?.error || 'Failed to create item. Please try again later.');
    }
  };

  // Handle opening file viewer
  const handleOpenViewer = async (document: Document) => {
    try {
      setFileName(document.name);
      setFileType(document.file_type);

      if (['txt', 'md', 'json', 'csv', 'xml', 'html', 'css', 'js', 'ts', 'jsx', 'tsx'].includes(document.file_type.toLowerCase())) {
        // Text file
        const response = await axios.get(`${API_URL}/documents/${document.id}/content`, {
          headers: {
            Authorization: `Bearer ${getAuthToken()}`
          }
        });

        setFileContent(response.data.content);
        setViewerOpen(true);
      } else if (document.file_type.toLowerCase() === 'pdf') {
        // PDF file
        setViewerOpen(true);
      } else {
        // Other file types - download
        handleDownloadDocument(document);
      }
    } catch (err: any) {
      console.error('Error opening file:', err);
      setError(err.response?.data?.error || 'Failed to open file. Please try again later.');
    }
  };

  // Handle opening file editor
  const handleOpenEditor = async (document: Document) => {
    try {
      setFileName(document.name);
      setFileType(document.file_type);

      if (['txt', 'md', 'json', 'csv', 'xml', 'html', 'css', 'js', 'ts', 'jsx', 'tsx'].includes(document.file_type.toLowerCase())) {
        // Text file
        const response = await axios.get(`${API_URL}/documents/${document.id}/content`, {
          headers: {
            Authorization: `Bearer ${getAuthToken()}`
          }
        });

        setFileContent(response.data.content);
        setEditorOpen(true);
      } else {
        setError('This file type cannot be edited in the browser.');
      }
    } catch (err: any) {
      console.error('Error opening file for editing:', err);
      setError(err.response?.data?.error || 'Failed to open file for editing. Please try again later.');
    }
  };

  // Handle saving edited file
  const handleSaveFile = async () => {
    try {
      // Find the document being edited
      const document = documents.find(doc => doc.name === fileName);
      if (!document) {
        setError('Document not found.');
        return;
      }

      // Create a blob from the edited content
      const blob = new Blob([fileContent], { type: 'text/plain' });
      const file = new File([blob], fileName, { type: 'text/plain' });

      // Create form data
      const formData = new FormData();
      formData.append('file', file);
      formData.append('description', document.description);
      if (document.folder_id) {
        formData.append('folderId', document.folder_id);
      }

      // Update the document
      await axios.put(
        `${API_URL}/documents/${document.id}`,
        formData,
        {
          headers: {
            Authorization: `Bearer ${getAuthToken()}`,
            'Content-Type': 'multipart/form-data'
          }
        }
      );

      setSuccessMessage('File saved successfully');
      setEditorOpen(false);

      // Refresh the document list
      const fetchData = async () => {
        try {
          // Fetch documents
          let documentsUrl = `${API_URL}/documents/farm/${currentFarm?.id || user?.farm_id}/documents`;
          const params: Record<string, string> = {};

          if (currentFolder) {
            params.folderId = currentFolder;
          } else if (currentFolder === null) {
            params.folderId = 'null'; // Explicitly request root documents
          }

          if (searchQuery) {
            params.search = searchQuery;
          }

          // Add query parameters if any filters are applied
          if (Object.keys(params).length > 0) {
            const queryString = new URLSearchParams(params).toString();
            documentsUrl = `${documentsUrl}?${queryString}`;
          }

          const documentsResponse = await axios.get(documentsUrl, {
            headers: {
              Authorization: `Bearer ${getAuthToken()}`
            }
          });

          setDocuments(documentsResponse.data.documents || []);
        } catch (err: any) {
          console.error('Error refreshing documents:', err);
          setError(err.response?.data?.error || 'Failed to refresh documents. Please try again later.');
        }
      };

      await fetchData();

      // Hide success message after 3 seconds
      if (successMessageTimeoutRef.current) {
        clearTimeout(successMessageTimeoutRef.current);
      }
      successMessageTimeoutRef.current = setTimeout(() => {
        setSuccessMessage(null);
        successMessageTimeoutRef.current = null;
      }, 3000);
    } catch (err: any) {
      console.error('Error saving file:', err);
      setError(err.response?.data?.error || 'Failed to save file. Please try again later.');
    }
  };

  // Handle PDF document loading
  const onDocumentLoadSuccess = ({ numPages }: { numPages: number }) => {
    setNumPages(numPages);
    setPageNumber(1);
  };

  // Handle page navigation
  const changePage = (offset: number) => {
    setPageNumber(prevPageNumber => {
      const newPageNumber = prevPageNumber + offset;
      return Math.min(Math.max(1, newPageNumber), numPages || 1);
    });
  };

  // Error handler for React errors
  const handleReactError = (error: Error, errorInfo: any) => {
    logReactError(error, errorInfo);
    setError(`React Error: ${error.message}`);
  };

  // Add a specific error handler for react-pdf related errors
  const handlePdfError = (error: Error, errorInfo: any) => {
    console.error('PDF Rendering Error:', error);
    console.error('Error Info:', errorInfo);

    // Log with React 19 compatibility checker if available
    if (typeof window !== 'undefined' && window.logReact19Error) {
      window.logReact19Error(error, 'PDF Rendering');
    }

    // Don't set the global error state to prevent the whole page from failing
    // Instead, the ErrorBoundary's fallback UI will be shown just for the PDF viewer
  };

  return (
    <ErrorBoundary onError={handleReactError}>
      <Layout>
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-gray-900">Documents</h1>
          <div className="flex space-x-2">
            <Link
              to="/documents/new"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Upload Document
            </Link>
            <Link
              to="/documents/folders/new"
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Create Folder
            </Link>
          </div>
        </div>

      {/* Breadcrumb and Search */}
      <div className="flex justify-between items-center mb-4">
        <div className="flex items-center space-x-2">
          {folderPath.map((folder, index) => (
            <div key={folder.id || 'root'} className="flex items-center">
              {index > 0 && <span className="text-gray-500 mx-2">/</span>}
              <button
                onClick={() => navigateToFolder(folder.id, folder.name)}
                className={`px-2 py-1 text-sm rounded ${
                  currentFolder === folder.id ? 'bg-primary-100 text-primary-700' : 'text-gray-600 hover:bg-gray-100'
                }`}
              >
                {folder.name}
              </button>
            </div>
          ))}
        </div>
        <div className="relative">
          <input
            type="text"
            placeholder="Search documents..."
            value={searchQuery}
            onChange={handleSearchChange}
            className="w-64 px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
          />
          <svg
            className="absolute right-3 top-2.5 h-5 w-5 text-gray-400"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
            />
          </svg>
        </div>
      </div>

      {/* Error message */}
      {error && (
        <div className="mb-4 p-4 text-sm text-red-700 bg-red-100 rounded-md flex items-center">
          <svg className="h-5 w-5 mr-2 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
          </svg>
          <span>{error}</span>
          <button 
            onClick={() => setError(null)} 
            className="ml-auto text-red-700 hover:text-red-900"
            aria-label="Dismiss error"
          >
            <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      )}

      {/* Success message */}
      {successMessage && (
        <div className="mb-4 p-4 text-sm text-green-700 bg-green-100 rounded-md flex items-center">
          <svg className="h-5 w-5 mr-2 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
          <span>{successMessage}</span>
          <button 
            onClick={() => setSuccessMessage(null)} 
            className="ml-auto text-green-700 hover:text-green-900"
            aria-label="Dismiss success message"
          >
            <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      )}

      {/* Loading state */}
      {loading ? (
        <div className="flex flex-col justify-center items-center h-64 bg-gray-50 rounded-lg border border-gray-200">
          <svg
            className="animate-spin h-10 w-10 text-primary-500 mb-4"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            ></circle>
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
          <p className="text-gray-600">Loading documents and folders...</p>
        </div>
      ) : (
        <ContextMenu
          items={[
            {
              label: 'Create New File',
              icon: (
                <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 13h6m-3-3v6m5 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  />
                </svg>
              ),
              onClick: () => {
                setCreateType('file');
                setNewItemName('');
                setNewItemDescription('');
                setFileContent('');
                setCreateModalOpen(true);
              }
            },
            {
              label: 'Create New Folder',
              icon: (
                <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 13h6m-3-3v6m-3.586-9H6a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V8a2 2 0 00-2-2h-1.586l-2-2H8.414l-2 2z"
                  />
                </svg>
              ),
              onClick: () => {
                setCreateType('folder');
                setNewItemName('');
                setNewItemDescription('');
                setCreateModalOpen(true);
              }
            },
            {
              label: 'Refresh',
              icon: (
                <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                  />
                </svg>
              ),
              onClick: () => {
                // Refresh the document list
                const fetchData = async () => {
                  setLoading(true);
                  try {
                    // Fetch documents
                    let documentsUrl = `${API_URL}/documents/farm/${currentFarm?.id || user?.farm_id}/documents`;
                    const params: Record<string, string> = {};

                    if (currentFolder) {
                      params.folderId = currentFolder;
                    } else if (currentFolder === null) {
                      params.folderId = 'null'; // Explicitly request root documents
                    }

                    if (searchQuery) {
                      params.search = searchQuery;
                    }

                    // Add query parameters if any filters are applied
                    if (Object.keys(params).length > 0) {
                      const queryString = new URLSearchParams(params).toString();
                      documentsUrl = `${documentsUrl}?${queryString}`;
                    }

                    const documentsResponse = await axios.get(documentsUrl, {
                      headers: {
                        Authorization: `Bearer ${getAuthToken()}`
                      }
                    });

                    setDocuments(documentsResponse.data.documents || []);

                    // Fetch subfolders
                    let foldersUrl = `${API_URL}/documents/farm/${currentFarm?.id || user?.farm_id}/folders`;
                    const folderParams: Record<string, string> = {};

                    if (currentFolder) {
                      folderParams.parentFolderId = currentFolder;
                    } else if (currentFolder === null) {
                      folderParams.parentFolderId = 'null'; // Explicitly request root folders
                    }

                    if (searchQuery) {
                      folderParams.search = searchQuery;
                    }

                    // Add query parameters if any filters are applied
                    if (Object.keys(folderParams).length > 0) {
                      const queryString = new URLSearchParams(folderParams).toString();
                      foldersUrl = `${foldersUrl}?${queryString}`;
                    }

                    const foldersResponse = await axios.get(foldersUrl, {
                      headers: {
                        Authorization: `Bearer ${getAuthToken()}`
                      }
                    });

                    setSubfolders(foldersResponse.data || []);
                  } catch (err: any) {
                    console.error('Error refreshing data:', err);
                    setError(err.response?.data?.error || 'Failed to refresh data. Please try again later.');
                  } finally {
                    setLoading(false);
                  }
                };

                fetchData();
              }
            }
          ]}
        >
          <div 
            ref={explorerRef}
            className="relative"
            onMouseDown={handleMouseDown}
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
            onContextMenu={handleExplorerContextMenu}
          >
            <div className="explorer-area min-h-[300px] h-[calc(100vh-200px)]">
              {/* Empty state */}
              {documents.length === 0 && subfolders.length === 0 && !loading && (
                <div className="flex flex-col items-center justify-center h-64 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300 explorer-area">
                  <svg
                    className="h-12 w-12 text-gray-400 mb-2"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 13h6m-3-3v6m5 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                    />
                  </svg>
                  <p className="text-gray-500 mb-2">No documents or folders found</p>
                  <div className="flex space-x-2">
                    <Link
                      to="/documents/new"
                      className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                    >
                      Upload document
                    </Link>
                    <button
                      onClick={() => {
                        setCreateType('folder');
                        setNewItemName('');
                        setNewItemDescription('');
                        setCreateModalOpen(true);
                      }}
                      className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                    >
                      Create folder
                    </button>
                  </div>
                </div>
              )}

              {/* Subfolders grid */}
              {subfolders.length > 0 && (
                <div className="mb-6">
                  <h2 className="text-lg font-medium text-gray-900 mb-3">Folders</h2>
                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                    {subfolders.map((folder) => (
                      <ContextMenu
                        key={folder.id}
                        items={[
                          {
                            label: 'Open Folder',
                            icon: (
                              <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M5 19a2 2 0 01-2-2V7a2 2 0 012-2h4l2 2h4a2 2 0 012 2v1M5 19h14a2 2 0 002-2v-5a2 2 0 00-2-2H9a2 2 0 00-2 2v5a2 2 0 01-2 2z"
                                />
                              </svg>
                            ),
                            onClick: () => navigateToFolder(folder.id, folder.name)
                          },
                          {
                            label: 'Edit Folder',
                            icon: (
                              <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                                />
                              </svg>
                            ),
                            onClick: () => navigate(`/documents/folders/edit/${folder.id}`)
                          },
                          {
                            label: 'Manage Permissions',
                            icon: (
                              <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                                />
                              </svg>
                            ),
                            onClick: () => navigate(`/documents/permissions/folder/${folder.id}`)
                          },
                          {
                            label: 'Delete Folder',
                            icon: (
                              <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                                />
                              </svg>
                            ),
                            onClick: () => navigate(`/documents/folders/delete/${folder.id}`),
                            danger: true
                          }
                        ]}
                      >
                        <div
                          className={`bg-white rounded-lg border ${selectedItems.folders.includes(folder.id) ? 'border-primary-500 ring-2 ring-primary-200' : 'border-gray-200'} shadow-sm hover:shadow-md transition-shadow folder-item`}
                          data-id={folder.id}
                          onClick={(e) => handleItemClick(e, 'folder', folder.id)}
                        >
                          <div className="p-4">
                            <div className="flex items-center justify-between mb-2">
                              <div 
                                className="text-primary-600 hover:text-primary-900 font-medium cursor-pointer"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  navigateToFolder(folder.id, folder.name);
                                }}
                                onDoubleClick={(e) => {
                                  e.stopPropagation();
                                  navigateToFolder(folder.id, folder.name);
                                }}
                              >
                                <div className="flex items-center">
                                  <svg
                                    className="h-6 w-6 text-gray-500 mr-2"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                  >
                                    <path
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth={2}
                                      d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z"
                                    />
                                  </svg>
                                  {folder.name}
                                </div>
                              </div>
                              <div className="flex space-x-2">
                                <Link
                                  to={`/documents/folders/edit/${folder.id}`}
                                  className="text-gray-600 hover:text-gray-900"
                                  onClick={(e) => e.stopPropagation()}
                                >
                                  <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth={2}
                                      d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                                    />
                                  </svg>
                                </Link>
                              </div>
                            </div>
                            <p className="text-sm text-gray-500 mb-2 line-clamp-2">
                              {folder.description || 'No description'}
                            </p>
                          </div>
                        </div>
                      </ContextMenu>
                    ))}
                  </div>
                </div>
              )}

              {/* Documents list */}
              {(documents.length > 0 || subfolders.length > 0) && (
                <div className="bg-white shadow overflow-hidden sm:rounded-md h-full max-h-[calc(100vh-200px)] overflow-y-auto" ref={documentListRef}>
                  <ul className="divide-y divide-gray-200">
                    {subfolders.map((folder) => (
                      <li key={`folder-${folder.id}`}>
                        <ContextMenu
                          items={[
                            {
                              label: 'Open Folder',
                              icon: (
                                <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M5 19a2 2 0 01-2-2V7a2 2 0 012-2h4l2 2h4a2 2 0 012 2v1M5 19h14a2 2 0 002-2v-5a2 2 0 00-2-2H9a2 2 0 00-2 2v5a2 2 0 01-2 2z"
                                  />
                                </svg>
                              ),
                              onClick: () => navigateToFolder(folder.id, folder.name)
                            },
                            {
                              label: 'Edit Folder',
                              icon: (
                                <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                                  />
                                </svg>
                              ),
                              onClick: () => navigate(`/documents/folders/edit/${folder.id}`)
                            },
                            {
                              label: 'Manage Permissions',
                              icon: (
                                <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                                  />
                                </svg>
                              ),
                              onClick: () => navigate(`/documents/permissions/folder/${folder.id}`)
                            },
                            {
                              label: 'Delete Folder',
                              icon: (
                                <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                                  />
                                </svg>
                              ),
                              onClick: () => navigate(`/documents/folders/delete/${folder.id}`),
                              danger: true
                            }
                          ]}
                        >
                          <div 
                            className={`px-4 py-4 sm:px-6 hover:bg-gray-50 cursor-pointer folder-item ${selectedItems.folders.includes(folder.id) ? 'bg-primary-50 border-l-4 border-primary-500' : ''}`}
                            data-id={folder.id}
                            onClick={(e) => handleItemClick(e, 'folder', folder.id)}
                          >
                            <div className="flex items-center justify-between">
                              <div className="flex items-center">
                                {/* Folder icon */}
                                <div className="flex-shrink-0 h-10 w-10 flex items-center justify-center rounded-md bg-gray-100 text-gray-500">
                                  <svg
                                    className="h-6 w-6 text-gray-500"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                  >
                                    <path
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth={2}
                                      d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z"
                                    />
                                  </svg>
                                </div>
                                <div className="ml-4">
                                  <div 
                                    className="text-sm font-medium text-primary-600 cursor-pointer"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      navigateToFolder(folder.id, folder.name);
                                    }}
                                  >
                                    {folder.name}
                                  </div>
                                  <div className="text-sm text-gray-500">
                                    {folder.description || 'No description'}
                                  </div>
                                </div>
                              </div>
                              <div className="flex items-center space-x-4">
                                <div className="text-sm text-gray-500">
                                  Folder
                                </div>
                                <div className="text-sm text-gray-500">
                                  {formatDate(folder.created_at)}
                                </div>
                                <div className="text-sm text-gray-500">
                                  {folder.creator?.first_name} {folder.creator?.last_name}
                                </div>
                                <div className="flex space-x-2">
                                  <Link
                                    to={`/documents/folders/edit/${folder.id}`}
                                    className="text-gray-600 hover:text-gray-900"
                                    onClick={(e) => e.stopPropagation()}
                                  >
                                    <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                      <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                                      />
                                    </svg>
                                  </Link>
                                </div>
                              </div>
                            </div>
                          </div>
                        </ContextMenu>
                      </li>
                    ))}
                    {documents.map((document) => (
                      <li key={document.id}>
                        <ContextMenu
                          items={[
                            {
                              label: 'View Document',
                              icon: (
                                <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                                  />
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                                  />
                                </svg>
                              ),
                              onClick: () => handleOpenViewer(document)
                            },
                            {
                              label: 'Download',
                              icon: (
                                <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
                                  />
                                </svg>
                              ),
                              onClick: () => handleDownloadDocument(document)
                            },
                            {
                              label: 'Edit Document',
                              icon: (
                                <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                                  />
                                </svg>
                              ),
                              onClick: () => handleOpenEditor(document)
                            },
                            {
                              label: 'Edit Details',
                              icon: (
                                <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                                  />
                                </svg>
                              ),
                              onClick: () => handleEditDocument(document)
                            },
                            {
                              label: 'Manage Permissions',
                              icon: (
                                <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                                  />
                                </svg>
                              ),
                              onClick: () => navigate(`/documents/permissions/document/${document.id}`)
                            },
                            {
                              label: 'Share Document',
                              icon: (
                                <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z"
                                  />
                                </svg>
                              ),
                              onClick: () => navigate(`/documents/share/${document.id}`)
                            },
                            ...(document.is_external ? [
                              {
                                label: 'Import to Local Storage',
                                icon: (
                                  <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth={2}
                                      d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4"
                                    />
                                  </svg>
                                ),
                                onClick: () => handleImportDocument(document)
                              }
                            ] : []),
                            {
                              label: 'Delete',
                              icon: (
                                <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                                  />
                                </svg>
                              ),
                              onClick: () => {
                                setSelectedDocument(document);
                                setDeleteConfirmOpen(true);
                              },
                              danger: true
                            }
                          ]}
                        >
                          <div 
                            className={`px-4 py-4 sm:px-6 hover:bg-gray-50 cursor-pointer document-item ${selectedItems.documents.includes(document.id) ? 'bg-primary-50 border-l-4 border-primary-500' : ''}`}
                            data-id={document.id}
                            onClick={(e) => handleItemClick(e, 'document', document.id)}
                            onDoubleClick={() => handleOpenViewer(document)}
                          >
                            <div className="flex items-center justify-between">
                              <div className="flex items-center">
                                {/* File type icon */}
                                <div className="flex-shrink-0 h-10 w-10 flex items-center justify-center rounded-md bg-gray-100 text-gray-500">
                                  {document.is_external ? (
                                    <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                      <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"
                                      />
                                    </svg>
                                  ) : (
                                    <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                      <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                                      />
                                    </svg>
                                  )}
                                </div>
                                <div className="ml-4">
                                  <div className="text-sm font-medium text-primary-600">
                                    {document.name}
                                  </div>
                                  <div className="text-sm text-gray-500">
                                    {document.description || 'No description'}
                                  </div>
                                </div>
                              </div>
                              <div className="flex items-center space-x-4">
                                <div className="text-sm text-gray-500">
                                  {formatFileSize(document.file_size)}
                                </div>
                                <div className="text-sm text-gray-500">
                                  {formatDate(document.created_at)}
                                </div>
                                <div className="text-sm text-gray-500">
                                  {document.uploader.first_name} {document.uploader.last_name}
                                </div>
                                <div className="flex space-x-2">
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleDownloadDocument(document);
                                    }}
                                    className="text-primary-600 hover:text-primary-900"
                                  >
                                    <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                      <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
                                      />
                                    </svg>
                                  </button>
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleOpenEditor(document);
                                    }}
                                    className="text-gray-600 hover:text-gray-900"
                                  >
                                    <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                      <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                                      />
                                    </svg>
                                  </button>
                                </div>
                              </div>
                            </div>
                          </div>
                        </ContextMenu>
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Selection box */}
              {selectionBox && (
                <div
                  className="absolute border-2 border-primary-500 bg-primary-100 bg-opacity-30 pointer-events-none"
                  style={{
                    left: `${selectionBox.left}px`,
                    top: `${selectionBox.top}px`,
                    width: `${selectionBox.width}px`,
                    height: `${selectionBox.height}px`,
                    zIndex: 10
                  }}
                />
              )}
            </div>
          </div>
        </ContextMenu>
      )}

      {/* Delete confirmation modal */}
      {deleteConfirmOpen && selectedDocument && (
        <div className="fixed z-10 inset-0 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-y-auto max-h-[90vh] shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="sm:flex sm:items-start">
                  <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                    <svg className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                    </svg>
                  </div>
                  <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                    <h3 className="text-lg leading-6 font-medium text-gray-900">
                      Delete document
                    </h3>
                    <div className="mt-2">
                      <p className="text-sm text-gray-500">
                        Are you sure you want to delete "{selectedDocument.name}"? This action cannot be undone.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  type="button"
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm"
                  onClick={() => {
                    handleDeleteDocument(selectedDocument);
                    setDeleteConfirmOpen(false);
                  }}
                >
                  Delete
                </button>
                <button
                  type="button"
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                  onClick={() => setDeleteConfirmOpen(false)}
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* File Viewer Modal */}
      {viewerOpen && (
        <div className="fixed z-10 inset-0 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-y-auto max-h-[90vh] shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg leading-6 font-medium text-gray-900">
                    {fileName}
                  </h3>
                  <button
                    type="button"
                    className="rounded-md text-gray-400 hover:text-gray-500 focus:outline-none"
                    onClick={() => setViewerOpen(false)}
                  >
                    <span className="sr-only">Close</span>
                    <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
                <div className="mt-2">
                  {fileType.toLowerCase() === 'pdf' ? (
                    <div className="flex flex-col items-center">
                      {(() => {
                        const foundDocument = documents.find(doc => doc.name === fileName);
                        if (!foundDocument?.id) {
                          return <div className="text-red-500">Error: Could not find document</div>;
                        }

                        // Use error boundary specifically for PDF rendering
                        return (
                          <ErrorBoundary
                            onError={handlePdfError}
                            fallback={
                              <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                                <div className="flex items-center mb-2">
                                  <svg className="h-6 w-6 text-yellow-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                  </svg>
                                  <h3 className="text-lg font-medium text-yellow-800">PDF Viewer Issue</h3>
                                </div>
                                <p className="text-yellow-700 mb-3">
                                  There was a problem displaying this PDF. You can still download the file to view it locally.
                                </p>
                                <button
                                  onClick={() => handleDownloadDocument(foundDocument)}
                                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-yellow-600 hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500"
                                >
                                  Download PDF
                                </button>
                              </div>
                            }
                          >
                            <PDFDocument
                              file={`${API_URL}/documents/${foundDocument.id}/download?token=${getAuthToken()}`}
                              onLoadSuccess={onDocumentLoadSuccess}
                              options={{
                                cMapUrl: 'https://cdn.jsdelivr.net/npm/pdfjs-dist@2.6.347/cmaps/',
                                cMapPacked: true,
                              }}
                            >
                              <Page pageNumber={pageNumber} />
                            </PDFDocument>
                          </ErrorBoundary>
                        );
                      })()}
                      <div className="mt-4 flex items-center justify-center">
                        <button
                          onClick={() => changePage(-1)}
                          disabled={pageNumber <= 1}
                          className={`px-4 py-2 border rounded-md ${pageNumber <= 1 ? 'text-gray-400 border-gray-300' : 'text-gray-700 border-gray-400 hover:bg-gray-100'}`}
                        >
                          Previous
                        </button>
                        <p className="mx-4">
                          Page {pageNumber} of {numPages}
                        </p>
                        <button
                          onClick={() => changePage(1)}
                          disabled={pageNumber >= (numPages || 1)}
                          className={`px-4 py-2 border rounded-md ${pageNumber >= (numPages || 1) ? 'text-gray-400 border-gray-300' : 'text-gray-700 border-gray-400 hover:bg-gray-100'}`}
                        >
                          Next
                        </button>
                      </div>
                    </div>
                  ) : (
                    <div className="bg-gray-50 p-4 rounded-md">
                      <pre className="whitespace-pre-wrap font-mono text-sm text-gray-800 max-h-[60vh] overflow-y-auto">
                        {fileContent}
                      </pre>
                    </div>
                  )}
                </div>
              </div>
              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  type="button"
                  className="w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:w-auto sm:text-sm"
                  onClick={() => setViewerOpen(false)}
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* File Editor Modal */}
      {editorOpen && (
        <div className="fixed z-10 inset-0 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-y-auto max-h-[90vh] shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg leading-6 font-medium text-gray-900">
                    Edit: {fileName}
                  </h3>
                  <button
                    type="button"
                    className="rounded-md text-gray-400 hover:text-gray-500 focus:outline-none"
                    onClick={() => setEditorOpen(false)}
                  >
                    <span className="sr-only">Close</span>
                    <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
                <div className="mt-2">
                  <textarea
                    value={fileContent}
                    onChange={(e) => setFileContent(e.target.value)}
                    className="w-full h-96 p-4 border border-gray-300 rounded-md font-mono text-sm focus:ring-primary-500 focus:border-primary-500"
                  />
                </div>
              </div>
              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  type="button"
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm"
                  onClick={handleSaveFile}
                >
                  Save
                </button>
                <button
                  type="button"
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                  onClick={() => setEditorOpen(false)}
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Create File/Folder Modal */}
      {createModalOpen && (
        <div className="fixed z-10 inset-0 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-y-auto max-h-[90vh] shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg leading-6 font-medium text-gray-900">
                    Create New {createType === 'folder' ? 'Folder' : 'File'}
                  </h3>
                  <button
                    type="button"
                    className="rounded-md text-gray-400 hover:text-gray-500 focus:outline-none"
                    onClick={() => setCreateModalOpen(false)}
                  >
                    <span className="sr-only">Close</span>
                    <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
                <div className="mt-2">
                  <div className="mb-4">
                    <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                      Name
                    </label>
                    <input
                      type="text"
                      id="name"
                      value={newItemName}
                      onChange={(e) => setNewItemName(e.target.value)}
                      className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                      placeholder={`Enter ${createType} name`}
                    />
                  </div>
                  <div className="mb-4">
                    <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                      Description
                    </label>
                    <input
                      type="text"
                      id="description"
                      value={newItemDescription}
                      onChange={(e) => setNewItemDescription(e.target.value)}
                      className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                      placeholder="Enter description (optional)"
                    />
                  </div>
                  {createType === 'file' && (
                    <div className="mb-4">
                      <label htmlFor="content" className="block text-sm font-medium text-gray-700">
                        Content
                      </label>
                      <textarea
                        id="content"
                        value={fileContent}
                        onChange={(e) => setFileContent(e.target.value)}
                        className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm h-40"
                        placeholder="Enter file content"
                      />
                    </div>
                  )}
                </div>
              </div>
              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  type="button"
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm"
                  onClick={handleCreateItem}
                >
                  Create
                </button>
                <button
                  type="button"
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                  onClick={() => setCreateModalOpen(false)}
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
      </Layout>
    </ErrorBoundary>
  );
};

export default DocumentList;
