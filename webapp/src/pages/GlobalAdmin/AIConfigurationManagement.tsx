import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { API_URL } from '../../config';
import { toast } from 'react-hot-toast';
import { Modal } from 'antd';
import { getAuthToken } from '../../utils/storageUtils';

// Define interfaces for our data types
interface AIProvider {
  id: string;
  name: string;
  description: string | null;
  api_base_url: string | null;
  auth_type: string;
  is_enabled: boolean;
  created_at: string;
  updated_at: string;
}

interface AIModel {
  id: string;
  provider_id: string;
  provider_name?: string;
  name: string;
  model_identifier: string;
  description: string | null;
  capabilities: string[];
  max_tokens: number | null;
  is_enabled: boolean;
  created_at: string;
  updated_at: string;
}

interface AIConfiguration {
  id: string;
  name: string;
  description: string | null;
  provider_id: string;
  provider_name?: string;
  default_model_id: string | null;
  model_name?: string | null;
  api_key: string | null;
  api_key_encrypted: boolean;
  additional_settings: any;
  is_global: boolean;
  is_enabled: boolean;
  created_at: string;
  updated_at: string;
}

interface AIInstruction {
  id: string;
  name: string;
  description: string | null;
  task_type: string;
  instructions: string;
  example_input: string | null;
  example_output: string | null;
  is_enabled: boolean;
  created_at: string;
  updated_at: string;
}

const AIConfigurationManagement: React.FC = () => {
  // State for active tab
  const [activeTab, setActiveTab] = useState<'providers' | 'models' | 'configurations' | 'instructions' | 'testing'>('providers');

  // State for data
  const [providers, setProviders] = useState<AIProvider[]>([]);
  const [models, setModels] = useState<AIModel[]>([]);
  const [configurations, setConfigurations] = useState<AIConfiguration[]>([]);
  const [instructions, setInstructions] = useState<AIInstruction[]>([]);

  // State for loading and error
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // State for testing
  const [testConfiguration, setTestConfiguration] = useState<string>('');
  const [testModel, setTestModel] = useState<string>('');
  const [testPrompt, setTestPrompt] = useState<string>('');
  const [testResponse, setTestResponse] = useState<string>('');
  const [testLoading, setTestLoading] = useState<boolean>(false);

  // State for edit modals
  const [editProviderModal, setEditProviderModal] = useState<boolean>(false);
  const [editModelModal, setEditModelModal] = useState<boolean>(false);
  const [editConfigurationModal, setEditConfigurationModal] = useState<boolean>(false);
  const [editInstructionModal, setEditInstructionModal] = useState<boolean>(false);

  // State for create modals
  const [createProviderModal, setCreateProviderModal] = useState<boolean>(false);
  const [createModelModal, setCreateModelModal] = useState<boolean>(false);
  const [createConfigurationModal, setCreateConfigurationModal] = useState<boolean>(false);
  const [createInstructionModal, setCreateInstructionModal] = useState<boolean>(false);

  // State for current item being edited
  const [currentProvider, setCurrentProvider] = useState<AIProvider | null>(null);
  const [currentModel, setCurrentModel] = useState<AIModel | null>(null);
  const [currentConfiguration, setCurrentConfiguration] = useState<AIConfiguration | null>(null);
  const [currentInstruction, setCurrentInstruction] = useState<AIInstruction | null>(null);

  // State for form inputs
  const [formProvider, setFormProvider] = useState<Partial<AIProvider>>({});
  const [formModel, setFormModel] = useState<Partial<AIModel>>({});
  const [formConfiguration, setFormConfiguration] = useState<Partial<AIConfiguration>>({});
  const [formInstruction, setFormInstruction] = useState<Partial<AIInstruction>>({});

  // Fetch data based on active tab
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        switch (activeTab) {
          case 'providers':
            const providersResponse = await axios.get(`${API_URL}/api/ai-configuration/providers`, {
              headers: {
                Authorization: `Bearer ${getAuthToken()}`
              }
            });
            setProviders(providersResponse.data || []);
            break;

          case 'models':
            const modelsResponse = await axios.get(`${API_URL}/api/ai-configuration/models`, {
              headers: {
                Authorization: `Bearer ${getAuthToken()}`
              }
            });
            setModels(modelsResponse.data || []);
            break;

          case 'configurations':
            const configurationsResponse = await axios.get(`${API_URL}/api/ai-configuration/configurations`, {
              headers: {
                Authorization: `Bearer ${getAuthToken()}`
              }
            });
            setConfigurations(configurationsResponse.data || []);
            break;

          case 'instructions':
            const instructionsResponse = await axios.get(`${API_URL}/api/ai-configuration/instructions`, {
              headers: {
                Authorization: `Bearer ${getAuthToken()}`
              }
            });
            setInstructions(instructionsResponse.data || []);
            break;
        }

        setLoading(false);
      } catch (err: any) {
        console.error(`Error fetching ${activeTab} data:`, err);
        setError(err.response?.data?.error || `Failed to load ${activeTab} data`);
        setLoading(false);
      }
    };

    fetchData();
  }, [activeTab]);

  // Handler for testing AI configuration
  const handleTestConfiguration = async () => {
    try {
      setTestLoading(true);
      setError(null);
      setTestResponse('');

      const response = await axios.post(`${API_URL}/api/ai-configuration/test`, { 
        configuration_id: testConfiguration,
        model_id: testModel || undefined,
        prompt: testPrompt
      }, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });

      setTestResponse(response.data.response);
      toast.success('Test completed successfully');
    } catch (err: any) {
      console.error('Error testing configuration:', err);
      setError(err.response?.data?.error || 'Failed to test configuration');
      toast.error(err.response?.data?.error || 'Failed to test configuration');
    } finally {
      setTestLoading(false);
    }
  };

  // Provider handlers
  const handleOpenCreateProvider = () => {
    setFormProvider({
      name: '',
      description: '',
      api_base_url: '',
      auth_type: 'api_key',
      is_enabled: true
    });
    setCreateProviderModal(true);
  };

  const handleOpenEditProvider = (provider: AIProvider) => {
    setCurrentProvider(provider);
    setFormProvider({
      name: provider.name,
      description: provider.description,
      api_base_url: provider.api_base_url,
      auth_type: provider.auth_type,
      is_enabled: provider.is_enabled
    });
    setEditProviderModal(true);
  };

  const handleProviderInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;

    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormProvider(prev => ({ ...prev, [name]: checked }));
    } else {
      setFormProvider(prev => ({ ...prev, [name]: value }));
    }
  };

  const handleCreateProvider = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await axios.post(`${API_URL}/api/ai-configuration/providers`, formProvider, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });

      // Add the new provider to the list
      setProviders(prev => [...prev, response.data]);

      // Close the modal and reset form
      setCreateProviderModal(false);
      setFormProvider({});

      toast.success('Provider created successfully');
    } catch (err: any) {
      console.error('Error creating provider:', err);
      setError(err.response?.data?.error || 'Failed to create provider');
      toast.error(err.response?.data?.error || 'Failed to create provider');
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateProvider = async () => {
    if (!currentProvider) return;

    try {
      setLoading(true);
      setError(null);

      const response = await axios.put(`${API_URL}/api/ai-configuration/providers/${currentProvider.id}`, formProvider, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });

      // Update the provider in the list
      setProviders(prev => prev.map(p => p.id === currentProvider.id ? response.data : p));

      // Close the modal and reset form
      setEditProviderModal(false);
      setCurrentProvider(null);
      setFormProvider({});

      toast.success('Provider updated successfully');
    } catch (err: any) {
      console.error('Error updating provider:', err);
      setError(err.response?.data?.error || 'Failed to update provider');
      toast.error(err.response?.data?.error || 'Failed to update provider');
    } finally {
      setLoading(false);
    }
  };

  const handleToggleProviderStatus = async (provider: AIProvider) => {
    try {
      setLoading(true);
      setError(null);

      const response = await axios.put(`${API_URL}/api/ai-configuration/providers/${provider.id}`, {
        is_enabled: !provider.is_enabled
      }, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });

      // Update the provider in the list
      setProviders(prev => prev.map(p => p.id === provider.id ? response.data : p));

      toast.success(`Provider ${response.data.is_enabled ? 'enabled' : 'disabled'} successfully`);
    } catch (err: any) {
      console.error('Error toggling provider status:', err);
      setError(err.response?.data?.error || 'Failed to update provider');
      toast.error(err.response?.data?.error || 'Failed to update provider');
    } finally {
      setLoading(false);
    }
  };

  // Model handlers
  const handleOpenCreateModel = () => {
    setFormModel({
      name: '',
      provider_id: providers.length > 0 ? providers[0].id : '',
      model_identifier: '',
      description: '',
      capabilities: [],
      max_tokens: null,
      is_enabled: true
    });
    setCreateModelModal(true);
  };

  const handleOpenEditModel = (model: AIModel) => {
    setCurrentModel(model);
    setFormModel({
      name: model.name,
      provider_id: model.provider_id,
      model_identifier: model.model_identifier,
      description: model.description,
      capabilities: model.capabilities,
      max_tokens: model.max_tokens,
      is_enabled: model.is_enabled
    });
    setEditModelModal(true);
  };

  const handleModelInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;

    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormModel(prev => ({ ...prev, [name]: checked }));
    } else if (name === 'capabilities') {
      // Handle capabilities as an array
      const capabilities = value.split(',').map(cap => cap.trim());
      setFormModel(prev => ({ ...prev, capabilities }));
    } else if (name === 'max_tokens') {
      // Handle max_tokens as a number or null
      const maxTokens = value ? parseInt(value, 10) : null;
      setFormModel(prev => ({ ...prev, max_tokens: maxTokens }));
    } else {
      setFormModel(prev => ({ ...prev, [name]: value }));
    }
  };

  const handleCreateModel = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await axios.post(`${API_URL}/api/ai-configuration/models`, formModel, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });

      // Add the new model to the list
      setModels(prev => [...prev, response.data]);

      // Close the modal and reset form
      setCreateModelModal(false);
      setFormModel({});

      toast.success('Model created successfully');
    } catch (err: any) {
      console.error('Error creating model:', err);
      setError(err.response?.data?.error || 'Failed to create model');
      toast.error(err.response?.data?.error || 'Failed to create model');
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateModel = async () => {
    if (!currentModel) return;

    try {
      setLoading(true);
      setError(null);

      const response = await axios.put(`${API_URL}/api/ai-configuration/models/${currentModel.id}`, formModel, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });

      // Update the model in the list
      setModels(prev => prev.map(m => m.id === currentModel.id ? response.data : m));

      // Close the modal and reset form
      setEditModelModal(false);
      setCurrentModel(null);
      setFormModel({});

      toast.success('Model updated successfully');
    } catch (err: any) {
      console.error('Error updating model:', err);
      setError(err.response?.data?.error || 'Failed to update model');
      toast.error(err.response?.data?.error || 'Failed to update model');
    } finally {
      setLoading(false);
    }
  };

  const handleToggleModelStatus = async (model: AIModel) => {
    try {
      setLoading(true);
      setError(null);

      const response = await axios.put(`${API_URL}/api/ai-configuration/models/${model.id}`, {
        is_enabled: !model.is_enabled
      }, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });

      // Update the model in the list
      setModels(prev => prev.map(m => m.id === model.id ? response.data : m));

      toast.success(`Model ${response.data.is_enabled ? 'enabled' : 'disabled'} successfully`);
    } catch (err: any) {
      console.error('Error toggling model status:', err);
      setError(err.response?.data?.error || 'Failed to update model');
      toast.error(err.response?.data?.error || 'Failed to update model');
    } finally {
      setLoading(false);
    }
  };

  // Configuration handlers
  const handleOpenCreateConfiguration = () => {
    setFormConfiguration({
      name: '',
      description: '',
      provider_id: providers.length > 0 ? providers[0].id : '',
      default_model_id: null,
      api_key: '',
      additional_settings: {},
      is_global: true,
      is_enabled: true
    });
    setCreateConfigurationModal(true);
  };

  const handleOpenEditConfiguration = (config: AIConfiguration) => {
    setCurrentConfiguration(config);
    setFormConfiguration({
      name: config.name,
      description: config.description,
      provider_id: config.provider_id,
      default_model_id: config.default_model_id,
      api_key: config.api_key,
      additional_settings: config.additional_settings,
      is_global: config.is_global,
      is_enabled: config.is_enabled
    });
    setEditConfigurationModal(true);
  };

  const handleConfigurationInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;

    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormConfiguration(prev => ({ ...prev, [name]: checked }));
    } else if (name === 'additional_settings') {
      try {
        // Try to parse as JSON
        const settings = JSON.parse(value);
        setFormConfiguration(prev => ({ ...prev, additional_settings: settings }));
      } catch (err) {
        // If not valid JSON, store as string
        setFormConfiguration(prev => ({ ...prev, additional_settings: value }));
      }
    } else if (name === 'default_model_id') {
      // Handle default_model_id as string or null
      const modelId = value || null;
      setFormConfiguration(prev => ({ ...prev, default_model_id: modelId }));
    } else {
      setFormConfiguration(prev => ({ ...prev, [name]: value }));
    }
  };

  const handleCreateConfiguration = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await axios.post(`${API_URL}/api/ai-configuration/configurations`, formConfiguration, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });

      // Add the new configuration to the list
      setConfigurations(prev => [...prev, response.data]);

      // Close the modal and reset form
      setCreateConfigurationModal(false);
      setFormConfiguration({});

      toast.success('Configuration created successfully');
    } catch (err: any) {
      console.error('Error creating configuration:', err);
      setError(err.response?.data?.error || 'Failed to create configuration');
      toast.error(err.response?.data?.error || 'Failed to create configuration');
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateConfiguration = async () => {
    if (!currentConfiguration) return;

    try {
      setLoading(true);
      setError(null);

      const response = await axios.put(`${API_URL}/api/ai-configuration/configurations/${currentConfiguration.id}`, formConfiguration, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });

      // Update the configuration in the list
      setConfigurations(prev => prev.map(c => c.id === currentConfiguration.id ? response.data : c));

      // Close the modal and reset form
      setEditConfigurationModal(false);
      setCurrentConfiguration(null);
      setFormConfiguration({});

      toast.success('Configuration updated successfully');
    } catch (err: any) {
      console.error('Error updating configuration:', err);
      setError(err.response?.data?.error || 'Failed to update configuration');
      toast.error(err.response?.data?.error || 'Failed to update configuration');
    } finally {
      setLoading(false);
    }
  };

  const handleToggleConfigurationStatus = async (config: AIConfiguration) => {
    try {
      setLoading(true);
      setError(null);

      const response = await axios.put(`${API_URL}/api/ai-configuration/configurations/${config.id}`, {
        is_enabled: !config.is_enabled
      }, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });

      // Update the configuration in the list
      setConfigurations(prev => prev.map(c => c.id === config.id ? response.data : c));

      toast.success(`Configuration ${response.data.is_enabled ? 'enabled' : 'disabled'} successfully`);
    } catch (err: any) {
      console.error('Error toggling configuration status:', err);
      setError(err.response?.data?.error || 'Failed to update configuration');
      toast.error(err.response?.data?.error || 'Failed to update configuration');
    } finally {
      setLoading(false);
    }
  };

  // Instruction handlers
  const handleOpenCreateInstruction = () => {
    setFormInstruction({
      name: '',
      description: '',
      task_type: '',
      instructions: '',
      example_input: '',
      example_output: '',
      is_enabled: true
    });
    setCreateInstructionModal(true);
  };

  const handleOpenEditInstruction = (instruction: AIInstruction) => {
    setCurrentInstruction(instruction);
    setFormInstruction({
      name: instruction.name,
      description: instruction.description,
      task_type: instruction.task_type,
      instructions: instruction.instructions,
      example_input: instruction.example_input,
      example_output: instruction.example_output,
      is_enabled: instruction.is_enabled
    });
    setEditInstructionModal(true);
  };

  const handleInstructionInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;

    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormInstruction(prev => ({ ...prev, [name]: checked }));
    } else {
      setFormInstruction(prev => ({ ...prev, [name]: value }));
    }
  };

  const handleCreateInstruction = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await axios.post(`${API_URL}/api/ai-configuration/instructions`, formInstruction, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });

      // Add the new instruction to the list
      setInstructions(prev => [...prev, response.data]);

      // Close the modal and reset form
      setCreateInstructionModal(false);
      setFormInstruction({});

      toast.success('Instruction created successfully');
    } catch (err: any) {
      console.error('Error creating instruction:', err);
      setError(err.response?.data?.error || 'Failed to create instruction');
      toast.error(err.response?.data?.error || 'Failed to create instruction');
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateInstruction = async () => {
    if (!currentInstruction) return;

    try {
      setLoading(true);
      setError(null);

      const response = await axios.put(`${API_URL}/api/ai-configuration/instructions/${currentInstruction.id}`, formInstruction, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });

      // Update the instruction in the list
      setInstructions(prev => prev.map(i => i.id === currentInstruction.id ? response.data : i));

      // Close the modal and reset form
      setEditInstructionModal(false);
      setCurrentInstruction(null);
      setFormInstruction({});

      toast.success('Instruction updated successfully');
    } catch (err: any) {
      console.error('Error updating instruction:', err);
      setError(err.response?.data?.error || 'Failed to update instruction');
      toast.error(err.response?.data?.error || 'Failed to update instruction');
    } finally {
      setLoading(false);
    }
  };

  const handleToggleInstructionStatus = async (instruction: AIInstruction) => {
    try {
      setLoading(true);
      setError(null);

      const response = await axios.put(`${API_URL}/api/ai-configuration/instructions/${instruction.id}`, {
        is_enabled: !instruction.is_enabled
      }, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });

      // Update the instruction in the list
      setInstructions(prev => prev.map(i => i.id === instruction.id ? response.data : i));

      toast.success(`Instruction ${response.data.is_enabled ? 'enabled' : 'disabled'} successfully`);
    } catch (err: any) {
      console.error('Error toggling instruction status:', err);
      setError(err.response?.data?.error || 'Failed to update instruction');
      toast.error(err.response?.data?.error || 'Failed to update instruction');
    } finally {
      setLoading(false);
    }
  };

  // Render loading state
  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
        <strong className="font-bold">Error!</strong>
        <span className="block sm:inline"> {error}</span>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold">AI Configuration Management</h2>
        <div className="flex space-x-2">
          <button
            className={`px-4 py-2 rounded-md ${activeTab === 'providers' ? 'bg-primary-600 text-white' : 'bg-gray-200 text-gray-800'}`}
            onClick={() => setActiveTab('providers')}
          >
            Providers
          </button>
          <button
            className={`px-4 py-2 rounded-md ${activeTab === 'models' ? 'bg-primary-600 text-white' : 'bg-gray-200 text-gray-800'}`}
            onClick={() => setActiveTab('models')}
          >
            Models
          </button>
          <button
            className={`px-4 py-2 rounded-md ${activeTab === 'configurations' ? 'bg-primary-600 text-white' : 'bg-gray-200 text-gray-800'}`}
            onClick={() => setActiveTab('configurations')}
          >
            Configurations
          </button>
          <button
            className={`px-4 py-2 rounded-md ${activeTab === 'instructions' ? 'bg-primary-600 text-white' : 'bg-gray-200 text-gray-800'}`}
            onClick={() => setActiveTab('instructions')}
          >
            Instructions
          </button>
          <button
            className={`px-4 py-2 rounded-md ${activeTab === 'testing' ? 'bg-primary-600 text-white' : 'bg-gray-200 text-gray-800'}`}
            onClick={() => setActiveTab('testing')}
          >
            Testing
          </button>
        </div>
      </div>

      {/* Provider Modals */}
      {/* Create Provider Modal */}
      <Modal
        open={createProviderModal}
        onCancel={() => setCreateProviderModal(false)}
        title="Create AI Provider"
        footer={null}
      >
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Name</label>
            <input
              type="text"
              name="name"
              value={formProvider.name || ''}
              onChange={handleProviderInputChange}
              className="w-full p-2 border border-gray-300 rounded-md"
              placeholder="Provider name"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
            <textarea
              name="description"
              value={formProvider.description || ''}
              onChange={handleProviderInputChange}
              className="w-full p-2 border border-gray-300 rounded-md"
              rows={3}
              placeholder="Provider description"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">API Base URL</label>
            <input
              type="text"
              name="api_base_url"
              value={formProvider.api_base_url || ''}
              onChange={handleProviderInputChange}
              className="w-full p-2 border border-gray-300 rounded-md"
              placeholder="https://api.example.com"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Authentication Type</label>
            <select
              name="auth_type"
              value={formProvider.auth_type || 'api_key'}
              onChange={handleProviderInputChange}
              className="w-full p-2 border border-gray-300 rounded-md"
            >
              <option value="api_key">API Key</option>
              <option value="oauth">OAuth</option>
              <option value="basic">Basic Auth</option>
            </select>
          </div>
          <div className="flex items-center">
            <input
              type="checkbox"
              name="is_enabled"
              checked={formProvider.is_enabled || false}
              onChange={handleProviderInputChange}
              className="h-4 w-4 text-primary-600 border-gray-300 rounded"
            />
            <label className="ml-2 block text-sm text-gray-900">Enabled</label>
          </div>
          <div className="flex justify-end space-x-2 pt-4">
            <button
              className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300"
              onClick={() => setCreateProviderModal(false)}
            >
              Cancel
            </button>
            <button
              className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700"
              onClick={handleCreateProvider}
            >
              Create
            </button>
          </div>
        </div>
      </Modal>

      {/* Edit Provider Modal */}
      <Modal
        open={editProviderModal}
        onCancel={() => setEditProviderModal(false)}
        title="Edit AI Provider"
        footer={null}
      >
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Name</label>
            <input
              type="text"
              name="name"
              value={formProvider.name || ''}
              onChange={handleProviderInputChange}
              className="w-full p-2 border border-gray-300 rounded-md"
              placeholder="Provider name"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
            <textarea
              name="description"
              value={formProvider.description || ''}
              onChange={handleProviderInputChange}
              className="w-full p-2 border border-gray-300 rounded-md"
              rows={3}
              placeholder="Provider description"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">API Base URL</label>
            <input
              type="text"
              name="api_base_url"
              value={formProvider.api_base_url || ''}
              onChange={handleProviderInputChange}
              className="w-full p-2 border border-gray-300 rounded-md"
              placeholder="https://api.example.com"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Authentication Type</label>
            <select
              name="auth_type"
              value={formProvider.auth_type || 'api_key'}
              onChange={handleProviderInputChange}
              className="w-full p-2 border border-gray-300 rounded-md"
            >
              <option value="api_key">API Key</option>
              <option value="oauth">OAuth</option>
              <option value="basic">Basic Auth</option>
            </select>
          </div>
          <div className="flex items-center">
            <input
              type="checkbox"
              name="is_enabled"
              checked={formProvider.is_enabled || false}
              onChange={handleProviderInputChange}
              className="h-4 w-4 text-primary-600 border-gray-300 rounded"
            />
            <label className="ml-2 block text-sm text-gray-900">Enabled</label>
          </div>
          <div className="flex justify-end space-x-2 pt-4">
            <button
              className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300"
              onClick={() => setEditProviderModal(false)}
            >
              Cancel
            </button>
            <button
              className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700"
              onClick={handleUpdateProvider}
            >
              Update
            </button>
          </div>
        </div>
      </Modal>

      {/* Model Modals */}
      {/* Create Model Modal */}
      <Modal
        open={createModelModal}
        onCancel={() => setCreateModelModal(false)}
        title="Create AI Model"
        footer={null}
      >
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Name</label>
            <input
              type="text"
              name="name"
              value={formModel.name || ''}
              onChange={handleModelInputChange}
              className="w-full p-2 border border-gray-300 rounded-md"
              placeholder="Model name"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Provider</label>
            <select
              name="provider_id"
              value={formModel.provider_id || ''}
              onChange={handleModelInputChange}
              className="w-full p-2 border border-gray-300 rounded-md"
            >
              {providers.map(provider => (
                <option key={provider.id} value={provider.id}>{provider.name}</option>
              ))}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Model Identifier</label>
            <input
              type="text"
              name="model_identifier"
              value={formModel.model_identifier || ''}
              onChange={handleModelInputChange}
              className="w-full p-2 border border-gray-300 rounded-md"
              placeholder="e.g., gpt-4, claude-2"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
            <textarea
              name="description"
              value={formModel.description || ''}
              onChange={handleModelInputChange}
              className="w-full p-2 border border-gray-300 rounded-md"
              rows={3}
              placeholder="Model description"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Capabilities (comma-separated)</label>
            <input
              type="text"
              name="capabilities"
              value={formModel.capabilities ? formModel.capabilities.join(', ') : ''}
              onChange={handleModelInputChange}
              className="w-full p-2 border border-gray-300 rounded-md"
              placeholder="e.g., text, images, audio"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Max Tokens</label>
            <input
              type="number"
              name="max_tokens"
              value={formModel.max_tokens || ''}
              onChange={handleModelInputChange}
              className="w-full p-2 border border-gray-300 rounded-md"
              placeholder="Leave empty for unlimited"
            />
          </div>
          <div className="flex items-center">
            <input
              type="checkbox"
              name="is_enabled"
              checked={formModel.is_enabled || false}
              onChange={handleModelInputChange}
              className="h-4 w-4 text-primary-600 border-gray-300 rounded"
            />
            <label className="ml-2 block text-sm text-gray-900">Enabled</label>
          </div>
          <div className="flex justify-end space-x-2 pt-4">
            <button
              className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300"
              onClick={() => setCreateModelModal(false)}
            >
              Cancel
            </button>
            <button
              className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700"
              onClick={handleCreateModel}
            >
              Create
            </button>
          </div>
        </div>
      </Modal>

      {/* Edit Model Modal */}
      <Modal
        open={editModelModal}
        onCancel={() => setEditModelModal(false)}
        title="Edit AI Model"
        footer={null}
      >
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Name</label>
            <input
              type="text"
              name="name"
              value={formModel.name || ''}
              onChange={handleModelInputChange}
              className="w-full p-2 border border-gray-300 rounded-md"
              placeholder="Model name"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Provider</label>
            <select
              name="provider_id"
              value={formModel.provider_id || ''}
              onChange={handleModelInputChange}
              className="w-full p-2 border border-gray-300 rounded-md"
            >
              {providers.map(provider => (
                <option key={provider.id} value={provider.id}>{provider.name}</option>
              ))}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Model Identifier</label>
            <input
              type="text"
              name="model_identifier"
              value={formModel.model_identifier || ''}
              onChange={handleModelInputChange}
              className="w-full p-2 border border-gray-300 rounded-md"
              placeholder="e.g., gpt-4, claude-2"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
            <textarea
              name="description"
              value={formModel.description || ''}
              onChange={handleModelInputChange}
              className="w-full p-2 border border-gray-300 rounded-md"
              rows={3}
              placeholder="Model description"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Capabilities (comma-separated)</label>
            <input
              type="text"
              name="capabilities"
              value={formModel.capabilities ? formModel.capabilities.join(', ') : ''}
              onChange={handleModelInputChange}
              className="w-full p-2 border border-gray-300 rounded-md"
              placeholder="e.g., text, images, audio"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Max Tokens</label>
            <input
              type="number"
              name="max_tokens"
              value={formModel.max_tokens || ''}
              onChange={handleModelInputChange}
              className="w-full p-2 border border-gray-300 rounded-md"
              placeholder="Leave empty for unlimited"
            />
          </div>
          <div className="flex items-center">
            <input
              type="checkbox"
              name="is_enabled"
              checked={formModel.is_enabled || false}
              onChange={handleModelInputChange}
              className="h-4 w-4 text-primary-600 border-gray-300 rounded"
            />
            <label className="ml-2 block text-sm text-gray-900">Enabled</label>
          </div>
          <div className="flex justify-end space-x-2 pt-4">
            <button
              className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300"
              onClick={() => setEditModelModal(false)}
            >
              Cancel
            </button>
            <button
              className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700"
              onClick={handleUpdateModel}
            >
              Update
            </button>
          </div>
        </div>
      </Modal>

      {/* Configuration Modals */}
      {/* Create Configuration Modal */}
      <Modal
        open={createConfigurationModal}
        onCancel={() => setCreateConfigurationModal(false)}
        title="Create AI Configuration"
        footer={null}
      >
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Name</label>
            <input
              type="text"
              name="name"
              value={formConfiguration.name || ''}
              onChange={handleConfigurationInputChange}
              className="w-full p-2 border border-gray-300 rounded-md"
              placeholder="Configuration name"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
            <textarea
              name="description"
              value={formConfiguration.description || ''}
              onChange={handleConfigurationInputChange}
              className="w-full p-2 border border-gray-300 rounded-md"
              rows={3}
              placeholder="Configuration description"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Provider</label>
            <select
              name="provider_id"
              value={formConfiguration.provider_id || ''}
              onChange={handleConfigurationInputChange}
              className="w-full p-2 border border-gray-300 rounded-md"
            >
              {providers.map(provider => (
                <option key={provider.id} value={provider.id}>{provider.name}</option>
              ))}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Default Model (Optional)</label>
            <select
              name="default_model_id"
              value={formConfiguration.default_model_id || ''}
              onChange={handleConfigurationInputChange}
              className="w-full p-2 border border-gray-300 rounded-md"
            >
              <option value="">No default model</option>
              {models.filter(model => model.provider_id === formConfiguration.provider_id).map(model => (
                <option key={model.id} value={model.id}>{model.name}</option>
              ))}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">API Key</label>
            <input
              type="password"
              name="api_key"
              value={formConfiguration.api_key || ''}
              onChange={handleConfigurationInputChange}
              className="w-full p-2 border border-gray-300 rounded-md"
              placeholder="API key for the provider"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Additional Settings (JSON)</label>
            <textarea
              name="additional_settings"
              value={typeof formConfiguration.additional_settings === 'object' 
                ? JSON.stringify(formConfiguration.additional_settings, null, 2) 
                : formConfiguration.additional_settings || '{}'}
              onChange={handleConfigurationInputChange}
              className="w-full p-2 border border-gray-300 rounded-md font-mono text-sm"
              rows={5}
              placeholder="{}"
            />
          </div>
          <div className="flex items-center">
            <input
              type="checkbox"
              name="is_global"
              checked={formConfiguration.is_global || false}
              onChange={handleConfigurationInputChange}
              className="h-4 w-4 text-primary-600 border-gray-300 rounded"
            />
            <label className="ml-2 block text-sm text-gray-900">Global Configuration</label>
          </div>
          <div className="flex items-center">
            <input
              type="checkbox"
              name="is_enabled"
              checked={formConfiguration.is_enabled || false}
              onChange={handleConfigurationInputChange}
              className="h-4 w-4 text-primary-600 border-gray-300 rounded"
            />
            <label className="ml-2 block text-sm text-gray-900">Enabled</label>
          </div>
          <div className="flex justify-end space-x-2 pt-4">
            <button
              className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300"
              onClick={() => setCreateConfigurationModal(false)}
            >
              Cancel
            </button>
            <button
              className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700"
              onClick={handleCreateConfiguration}
            >
              Create
            </button>
          </div>
        </div>
      </Modal>

      {/* Edit Configuration Modal */}
      <Modal
        open={editConfigurationModal}
        onCancel={() => setEditConfigurationModal(false)}
        title="Edit AI Configuration"
        footer={null}
      >
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Name</label>
            <input
              type="text"
              name="name"
              value={formConfiguration.name || ''}
              onChange={handleConfigurationInputChange}
              className="w-full p-2 border border-gray-300 rounded-md"
              placeholder="Configuration name"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
            <textarea
              name="description"
              value={formConfiguration.description || ''}
              onChange={handleConfigurationInputChange}
              className="w-full p-2 border border-gray-300 rounded-md"
              rows={3}
              placeholder="Configuration description"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Provider</label>
            <select
              name="provider_id"
              value={formConfiguration.provider_id || ''}
              onChange={handleConfigurationInputChange}
              className="w-full p-2 border border-gray-300 rounded-md"
            >
              {providers.map(provider => (
                <option key={provider.id} value={provider.id}>{provider.name}</option>
              ))}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Default Model (Optional)</label>
            <select
              name="default_model_id"
              value={formConfiguration.default_model_id || ''}
              onChange={handleConfigurationInputChange}
              className="w-full p-2 border border-gray-300 rounded-md"
            >
              <option value="">No default model</option>
              {models.filter(model => model.provider_id === formConfiguration.provider_id).map(model => (
                <option key={model.id} value={model.id}>{model.name}</option>
              ))}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">API Key</label>
            <input
              type="password"
              name="api_key"
              value={formConfiguration.api_key || ''}
              onChange={handleConfigurationInputChange}
              className="w-full p-2 border border-gray-300 rounded-md"
              placeholder="API key for the provider"
            />
            <p className="text-xs text-gray-500 mt-1">Leave empty to keep the current API key</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Additional Settings (JSON)</label>
            <textarea
              name="additional_settings"
              value={typeof formConfiguration.additional_settings === 'object' 
                ? JSON.stringify(formConfiguration.additional_settings, null, 2) 
                : formConfiguration.additional_settings || '{}'}
              onChange={handleConfigurationInputChange}
              className="w-full p-2 border border-gray-300 rounded-md font-mono text-sm"
              rows={5}
              placeholder="{}"
            />
          </div>
          <div className="flex items-center">
            <input
              type="checkbox"
              name="is_global"
              checked={formConfiguration.is_global || false}
              onChange={handleConfigurationInputChange}
              className="h-4 w-4 text-primary-600 border-gray-300 rounded"
            />
            <label className="ml-2 block text-sm text-gray-900">Global Configuration</label>
          </div>
          <div className="flex items-center">
            <input
              type="checkbox"
              name="is_enabled"
              checked={formConfiguration.is_enabled || false}
              onChange={handleConfigurationInputChange}
              className="h-4 w-4 text-primary-600 border-gray-300 rounded"
            />
            <label className="ml-2 block text-sm text-gray-900">Enabled</label>
          </div>
          <div className="flex justify-end space-x-2 pt-4">
            <button
              className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300"
              onClick={() => setEditConfigurationModal(false)}
            >
              Cancel
            </button>
            <button
              className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700"
              onClick={handleUpdateConfiguration}
            >
              Update
            </button>
          </div>
        </div>
      </Modal>

      {/* Instruction Modals */}
      {/* Create Instruction Modal */}
      <Modal
        open={createInstructionModal}
        onCancel={() => setCreateInstructionModal(false)}
        title="Create AI Instruction"
        footer={null}
      >
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Name</label>
            <input
              type="text"
              name="name"
              value={formInstruction.name || ''}
              onChange={handleInstructionInputChange}
              className="w-full p-2 border border-gray-300 rounded-md"
              placeholder="Instruction name"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
            <textarea
              name="description"
              value={formInstruction.description || ''}
              onChange={handleInstructionInputChange}
              className="w-full p-2 border border-gray-300 rounded-md"
              rows={3}
              placeholder="Instruction description"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Task Type</label>
            <input
              type="text"
              name="task_type"
              value={formInstruction.task_type || ''}
              onChange={handleInstructionInputChange}
              className="w-full p-2 border border-gray-300 rounded-md"
              placeholder="e.g., crop_rotation, soil_analysis"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Instructions</label>
            <textarea
              name="instructions"
              value={formInstruction.instructions || ''}
              onChange={handleInstructionInputChange}
              className="w-full p-2 border border-gray-300 rounded-md"
              rows={6}
              placeholder="Detailed instructions for the AI model"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Example Input (Optional)</label>
            <textarea
              name="example_input"
              value={formInstruction.example_input || ''}
              onChange={handleInstructionInputChange}
              className="w-full p-2 border border-gray-300 rounded-md"
              rows={3}
              placeholder="Example input for the AI model"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Example Output (Optional)</label>
            <textarea
              name="example_output"
              value={formInstruction.example_output || ''}
              onChange={handleInstructionInputChange}
              className="w-full p-2 border border-gray-300 rounded-md"
              rows={3}
              placeholder="Example output from the AI model"
            />
          </div>
          <div className="flex items-center">
            <input
              type="checkbox"
              name="is_enabled"
              checked={formInstruction.is_enabled || false}
              onChange={handleInstructionInputChange}
              className="h-4 w-4 text-primary-600 border-gray-300 rounded"
            />
            <label className="ml-2 block text-sm text-gray-900">Enabled</label>
          </div>
          <div className="flex justify-end space-x-2 pt-4">
            <button
              className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300"
              onClick={() => setCreateInstructionModal(false)}
            >
              Cancel
            </button>
            <button
              className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700"
              onClick={handleCreateInstruction}
            >
              Create
            </button>
          </div>
        </div>
      </Modal>

      {/* Edit Instruction Modal */}
      <Modal
        open={editInstructionModal}
        onCancel={() => setEditInstructionModal(false)}
        title="Edit AI Instruction"
        footer={null}
      >
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Name</label>
            <input
              type="text"
              name="name"
              value={formInstruction.name || ''}
              onChange={handleInstructionInputChange}
              className="w-full p-2 border border-gray-300 rounded-md"
              placeholder="Instruction name"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
            <textarea
              name="description"
              value={formInstruction.description || ''}
              onChange={handleInstructionInputChange}
              className="w-full p-2 border border-gray-300 rounded-md"
              rows={3}
              placeholder="Instruction description"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Task Type</label>
            <input
              type="text"
              name="task_type"
              value={formInstruction.task_type || ''}
              onChange={handleInstructionInputChange}
              className="w-full p-2 border border-gray-300 rounded-md"
              placeholder="e.g., crop_rotation, soil_analysis"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Instructions</label>
            <textarea
              name="instructions"
              value={formInstruction.instructions || ''}
              onChange={handleInstructionInputChange}
              className="w-full p-2 border border-gray-300 rounded-md"
              rows={6}
              placeholder="Detailed instructions for the AI model"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Example Input (Optional)</label>
            <textarea
              name="example_input"
              value={formInstruction.example_input || ''}
              onChange={handleInstructionInputChange}
              className="w-full p-2 border border-gray-300 rounded-md"
              rows={3}
              placeholder="Example input for the AI model"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Example Output (Optional)</label>
            <textarea
              name="example_output"
              value={formInstruction.example_output || ''}
              onChange={handleInstructionInputChange}
              className="w-full p-2 border border-gray-300 rounded-md"
              rows={3}
              placeholder="Example output from the AI model"
            />
          </div>
          <div className="flex items-center">
            <input
              type="checkbox"
              name="is_enabled"
              checked={formInstruction.is_enabled || false}
              onChange={handleInstructionInputChange}
              className="h-4 w-4 text-primary-600 border-gray-300 rounded"
            />
            <label className="ml-2 block text-sm text-gray-900">Enabled</label>
          </div>
          <div className="flex justify-end space-x-2 pt-4">
            <button
              className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300"
              onClick={() => setEditInstructionModal(false)}
            >
              Cancel
            </button>
            <button
              className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700"
              onClick={handleUpdateInstruction}
            >
              Update
            </button>
          </div>
        </div>
      </Modal>

      {/* Providers Tab */}
      {activeTab === 'providers' && (
        <div>
          <div className="flex justify-end mb-4">
            <button
              className="bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 transition-colors"
              onClick={handleOpenCreateProvider}
            >
              Add Provider
            </button>
          </div>

          <div className="bg-white shadow-md rounded-lg overflow-hidden">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Provider
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Auth Type
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {providers.map(provider => (
                  <tr key={provider.id}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="w-8 h-8 bg-gray-200 rounded-full mr-3 flex items-center justify-center">
                          <span className="text-gray-500">{provider.name.charAt(0)}</span>
                        </div>
                        <div>
                          <div className="font-medium text-gray-900">{provider.name}</div>
                          {provider.description && (
                            <div className="text-sm text-gray-500">{provider.description}</div>
                          )}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {provider.auth_type}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        provider.is_enabled 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {provider.is_enabled ? 'Enabled' : 'Disabled'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex justify-end space-x-2">
                        <button
                          className={`px-3 py-1 rounded ${
                            provider.is_enabled 
                              ? 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200' 
                              : 'bg-green-100 text-green-800 hover:bg-green-200'
                          }`}
                          onClick={() => handleToggleProviderStatus(provider)}
                        >
                          {provider.is_enabled ? 'Disable' : 'Enable'}
                        </button>
                        <button
                          className="px-3 py-1 rounded bg-blue-100 text-blue-800 hover:bg-blue-200"
                          onClick={() => handleOpenEditProvider(provider)}
                        >
                          Edit
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Models Tab */}
      {activeTab === 'models' && (
        <div>
          <div className="flex justify-end mb-4">
            <button
              className="bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 transition-colors"
              onClick={handleOpenCreateModel}
            >
              Add Model
            </button>
          </div>

          <div className="bg-white shadow-md rounded-lg overflow-hidden">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Model
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Provider
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Identifier
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {models.map(model => (
                  <tr key={model.id}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="font-medium text-gray-900">{model.name}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {model.provider_name}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {model.model_identifier}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        model.is_enabled 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {model.is_enabled ? 'Enabled' : 'Disabled'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex justify-end space-x-2">
                        <button
                          className={`px-3 py-1 rounded ${
                            model.is_enabled 
                              ? 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200' 
                              : 'bg-green-100 text-green-800 hover:bg-green-200'
                          }`}
                          onClick={() => handleToggleModelStatus(model)}
                        >
                          {model.is_enabled ? 'Disable' : 'Enable'}
                        </button>
                        <button
                          className="px-3 py-1 rounded bg-blue-100 text-blue-800 hover:bg-blue-200"
                          onClick={() => handleOpenEditModel(model)}
                        >
                          Edit
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Configurations Tab */}
      {activeTab === 'configurations' && (
        <div>
          <div className="flex justify-end mb-4">
            <button
              className="bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 transition-colors"
              onClick={handleOpenCreateConfiguration}
            >
              Add Configuration
            </button>
          </div>

          <div className="bg-white shadow-md rounded-lg overflow-hidden">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Configuration
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Provider
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Default Model
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Type
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {configurations.map(config => (
                  <tr key={config.id}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="font-medium text-gray-900">{config.name}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {config.provider_name}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {config.model_name || 'None'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        config.is_enabled 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {config.is_enabled ? 'Enabled' : 'Disabled'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        config.is_global 
                          ? 'bg-purple-100 text-purple-800' 
                          : 'bg-orange-100 text-orange-800'
                      }`}>
                        {config.is_global ? 'Global' : 'Farm-specific'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex justify-end space-x-2">
                        <button
                          className={`px-3 py-1 rounded ${
                            config.is_enabled 
                              ? 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200' 
                              : 'bg-green-100 text-green-800 hover:bg-green-200'
                          }`}
                          onClick={() => handleToggleConfigurationStatus(config)}
                        >
                          {config.is_enabled ? 'Disable' : 'Enable'}
                        </button>
                        <button
                          className="px-3 py-1 rounded bg-blue-100 text-blue-800 hover:bg-blue-200"
                          onClick={() => handleOpenEditConfiguration(config)}
                        >
                          Edit
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Instructions Tab */}
      {activeTab === 'instructions' && (
        <div>
          <div className="flex justify-end mb-4">
            <button
              className="bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 transition-colors"
              onClick={handleOpenCreateInstruction}
            >
              Add Instruction
            </button>
          </div>

          <div className="bg-white shadow-md rounded-lg overflow-hidden">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Instruction
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Task Type
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {instructions.map(instruction => (
                  <tr key={instruction.id}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="font-medium text-gray-900">{instruction.name}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {instruction.task_type}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        instruction.is_enabled 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {instruction.is_enabled ? 'Enabled' : 'Disabled'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex justify-end space-x-2">
                        <button
                          className={`px-3 py-1 rounded ${
                            instruction.is_enabled 
                              ? 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200' 
                              : 'bg-green-100 text-green-800 hover:bg-green-200'
                          }`}
                          onClick={() => handleToggleInstructionStatus(instruction)}
                        >
                          {instruction.is_enabled ? 'Disable' : 'Enable'}
                        </button>
                        <button
                          className="px-3 py-1 rounded bg-blue-100 text-blue-800 hover:bg-blue-200"
                          onClick={() => handleOpenEditInstruction(instruction)}
                        >
                          Edit
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Testing Tab */}
      {activeTab === 'testing' && (
        <div className="bg-white shadow-md rounded-lg p-6">
          <h3 className="text-lg font-semibold mb-4">Test AI Configuration</h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Configuration</label>
              <select
                className="w-full p-2 border border-gray-300 rounded-md"
                value={testConfiguration}
                onChange={(e) => setTestConfiguration(e.target.value)}
              >
                <option value="">Select a configuration</option>
                {configurations.map(config => (
                  <option key={config.id} value={config.id}>{config.name}</option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Model (Optional)</label>
              <select
                className="w-full p-2 border border-gray-300 rounded-md"
                value={testModel}
                onChange={(e) => setTestModel(e.target.value)}
              >
                <option value="">Use default model</option>
                {models.map(model => (
                  <option key={model.id} value={model.id}>{model.name}</option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Prompt</label>
              <textarea
                className="w-full p-2 border border-gray-300 rounded-md"
                rows={4}
                value={testPrompt}
                onChange={(e) => setTestPrompt(e.target.value)}
                placeholder="Enter your prompt here..."
              />
            </div>
            <div>
              <button
                className="bg-primary-600 text-white py-2 px-4 rounded-md hover:bg-primary-700 transition-colors"
                onClick={handleTestConfiguration}
                disabled={testLoading || !testConfiguration || !testPrompt}
              >
                {testLoading ? 'Testing...' : 'Test'}
              </button>
            </div>
            {testResponse && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Response</label>
                <div className="w-full p-4 bg-gray-50 border border-gray-300 rounded-md whitespace-pre-wrap">
                  {testResponse}
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default AIConfigurationManagement;
