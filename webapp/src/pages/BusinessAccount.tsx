import { useState, useEffect, useContext } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import { AuthContext } from '../context/AuthContext';
import Layout from '../components/Layout';
import { API_URL } from '../config';
import { getAuthToken } from '../utils/storageUtils';

interface BusinessAccount {
  id: string;
  name: string;
  address: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  taxId: string;
  phoneNumber: string;
  email: string;
  website: string;
  industry: string;
  logo: string;
}

const BusinessAccount = () => {
  const [businessAccount, setBusinessAccount] = useState<BusinessAccount | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [formData, setFormData] = useState<Partial<BusinessAccount>>({});
  const [isEditing, setIsEditing] = useState(false);

  const { user } = useContext(AuthContext);
  const navigate = useNavigate();

  useEffect(() => {
    // Check if user is authenticated
    if (!user) {
      navigate('/login');
      return;
    }

    const fetchBusinessAccount = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch business account information
        const response = await axios.get(`${API_URL}/business-account`, {
          headers: {
            Authorization: `Bearer ${getAuthToken()}`
          }
        });

        const businessData = response.data.businessAccount;
        setBusinessAccount(businessData);
        setFormData(businessData || {});
      } catch (err: any) {
        console.error('Error fetching business account data:', err);
        // Only set error for server errors, not for missing business account
        if (err.response?.status !== 404) {
          setError(err.response?.data?.error || 'Failed to load business account information');
        }
        // Initialize with empty data
        setFormData({});
      } finally {
        setLoading(false);
      }
    };

    fetchBusinessAccount();
  }, [user, navigate]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setSuccess(null);

    try {
      const method = businessAccount ? 'put' : 'post';
      const url = businessAccount 
        ? `${API_URL}/business-account/${businessAccount.id}` 
        : `${API_URL}/business-account`;

      const response = await axios({
        method,
        url,
        data: formData,
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });

      setBusinessAccount(response.data.businessAccount);
      setSuccess('Business account information saved successfully!');
      setIsEditing(false);

      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    } catch (err: any) {
      console.error('Error saving business account data:', err);
      setError(err.response?.data?.error || 'Failed to save business account information');
    }
  };

  const handleCancel = () => {
    setFormData(businessAccount || {});
    setIsEditing(false);
    setError(null);
  };

  return (
    <Layout>
      <div className="max-w-4xl mx-auto">
        <h1 className="text-2xl font-bold text-gray-900 mb-6">Business Account Management</h1>

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
            <span className="block sm:inline">{error}</span>
          </div>
        )}

        {success && (
          <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
            <span className="block sm:inline">{success}</span>
          </div>
        )}

        {loading ? (
          <div className="p-8 text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
            <p className="mt-2 text-gray-500">Loading business account information...</p>
          </div>
        ) : (
          <div className="bg-white shadow rounded-lg overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200 bg-gray-50 flex justify-between items-center">
              <h2 className="text-lg font-medium text-gray-900">Business Information</h2>
              {!isEditing && (
                <button
                  type="button"
                  onClick={() => setIsEditing(true)}
                  className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                >
                  Edit Information
                </button>
              )}
            </div>

            <div className="p-6">
              {isEditing ? (
                <form onSubmit={handleSubmit}>
                  <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    <div>
                      <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">Business Name</label>
                      <input
                        id="name"
                        name="name"
                        type="text"
                        required
                        className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                        value={formData.name || ''}
                        onChange={handleInputChange}
                      />
                    </div>

                    <div>
                      <label htmlFor="industry" className="block text-sm font-medium text-gray-700 mb-1">Industry</label>
                      <select
                        id="industry"
                        name="industry"
                        className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                        value={formData.industry || ''}
                        onChange={handleInputChange}
                      >
                        <option value="">Select Industry</option>
                        <option value="crop_farming">Crop Farming</option>
                        <option value="livestock">Livestock</option>
                        <option value="dairy_farming">Dairy Farming</option>
                        <option value="poultry_farming">Poultry Farming</option>
                        <option value="mixed_farming">Mixed Farming</option>
                        <option value="organic_farming">Organic Farming</option>
                        <option value="aquaculture">Aquaculture</option>
                        <option value="horticulture">Horticulture</option>
                        <option value="other">Other</option>
                      </select>
                    </div>

                    <div>
                      <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">Business Email</label>
                      <input
                        id="email"
                        name="email"
                        type="email"
                        className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                        value={formData.email || ''}
                        onChange={handleInputChange}
                      />
                    </div>

                    <div>
                      <label htmlFor="phoneNumber" className="block text-sm font-medium text-gray-700 mb-1">Business Phone</label>
                      <input
                        id="phoneNumber"
                        name="phoneNumber"
                        type="tel"
                        className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                        value={formData.phoneNumber || ''}
                        onChange={handleInputChange}
                      />
                    </div>

                    <div>
                      <label htmlFor="website" className="block text-sm font-medium text-gray-700 mb-1">Website</label>
                      <input
                        id="website"
                        name="website"
                        type="url"
                        className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                        value={formData.website || ''}
                        onChange={handleInputChange}
                      />
                    </div>

                    <div>
                      <label htmlFor="taxId" className="block text-sm font-medium text-gray-700 mb-1">Tax ID / EIN</label>
                      <input
                        id="taxId"
                        name="taxId"
                        type="text"
                        className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                        value={formData.taxId || ''}
                        onChange={handleInputChange}
                      />
                    </div>

                    <div className="sm:col-span-2">
                      <label htmlFor="address" className="block text-sm font-medium text-gray-700 mb-1">Address</label>
                      <input
                        id="address"
                        name="address"
                        type="text"
                        className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                        value={formData.address || ''}
                        onChange={handleInputChange}
                      />
                    </div>

                    <div>
                      <label htmlFor="city" className="block text-sm font-medium text-gray-700 mb-1">City</label>
                      <input
                        id="city"
                        name="city"
                        type="text"
                        className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                        value={formData.city || ''}
                        onChange={handleInputChange}
                      />
                    </div>

                    <div>
                      <label htmlFor="state" className="block text-sm font-medium text-gray-700 mb-1">State / Province</label>
                      <input
                        id="state"
                        name="state"
                        type="text"
                        className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                        value={formData.state || ''}
                        onChange={handleInputChange}
                      />
                    </div>

                    <div>
                      <label htmlFor="zipCode" className="block text-sm font-medium text-gray-700 mb-1">ZIP / Postal Code</label>
                      <input
                        id="zipCode"
                        name="zipCode"
                        type="text"
                        className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                        value={formData.zipCode || ''}
                        onChange={handleInputChange}
                      />
                    </div>

                    <div>
                      <label htmlFor="country" className="block text-sm font-medium text-gray-700 mb-1">Country</label>
                      <input
                        id="country"
                        name="country"
                        type="text"
                        className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                        value={formData.country || ''}
                        onChange={handleInputChange}
                      />
                    </div>
                  </div>

                  <div className="mt-6 flex items-center space-x-4">
                    <button
                      type="submit"
                      className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                    >
                      Save Changes
                    </button>
                    <button
                      type="button"
                      onClick={handleCancel}
                      className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                    >
                      Cancel
                    </button>
                  </div>
                </form>
              ) : (
                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                  {businessAccount ? (
                    <>
                      <div>
                        <h3 className="text-sm font-medium text-gray-500">Business Name</h3>
                        <p className="mt-1 text-sm text-gray-900">{businessAccount.name || 'Not specified'}</p>
                      </div>

                      <div>
                        <h3 className="text-sm font-medium text-gray-500">Industry</h3>
                        <p className="mt-1 text-sm text-gray-900">{businessAccount.industry ? businessAccount.industry.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()) : 'Not specified'}</p>
                      </div>

                      <div>
                        <h3 className="text-sm font-medium text-gray-500">Business Email</h3>
                        <p className="mt-1 text-sm text-gray-900">{businessAccount.email || 'Not specified'}</p>
                      </div>

                      <div>
                        <h3 className="text-sm font-medium text-gray-500">Business Phone</h3>
                        <p className="mt-1 text-sm text-gray-900">{businessAccount.phoneNumber || 'Not specified'}</p>
                      </div>

                      <div>
                        <h3 className="text-sm font-medium text-gray-500">Website</h3>
                        <p className="mt-1 text-sm text-gray-900">
                          {businessAccount.website ? (
                            <a href={businessAccount.website} target="_blank" rel="noopener noreferrer" className="text-primary-600 hover:text-primary-500">
                              {businessAccount.website}
                            </a>
                          ) : 'Not specified'}
                        </p>
                      </div>

                      <div>
                        <h3 className="text-sm font-medium text-gray-500">Tax ID / EIN</h3>
                        <p className="mt-1 text-sm text-gray-900">{businessAccount.taxId || 'Not specified'}</p>
                      </div>

                      <div className="sm:col-span-2">
                        <h3 className="text-sm font-medium text-gray-500">Address</h3>
                        <p className="mt-1 text-sm text-gray-900">
                          {businessAccount.address ? (
                            <>
                              {businessAccount.address}<br />
                              {businessAccount.city}{businessAccount.state ? `, ${businessAccount.state}` : ''} {businessAccount.zipCode}<br />
                              {businessAccount.country}
                            </>
                          ) : 'Not specified'}
                        </p>
                      </div>
                    </>
                  ) : (
                    <div className="sm:col-span-2 text-center py-6">
                      <p className="text-gray-500 mb-4">No business account information has been set up yet.</p>
                      <button
                        type="button"
                        onClick={() => setIsEditing(true)}
                        className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                      >
                        Set Up Business Account
                      </button>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        )}

        {/* Team Members Section - Could be implemented in the future */}
        <div className="mt-8 bg-white shadow rounded-lg overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
            <h2 className="text-lg font-medium text-gray-900">Team Members</h2>
          </div>

          <div className="p-6">
            <p className="text-gray-500 text-center py-6">
              Team member management will be available in a future update.
            </p>
          </div>
        </div>

        {/* Billing Information Section - Could be implemented in the future */}
        <div className="mt-8 bg-white shadow rounded-lg overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
            <h2 className="text-lg font-medium text-gray-900">Billing Information</h2>
          </div>

          <div className="p-6">
            <p className="text-gray-500 text-center py-6">
              Billing information management will be available in a future update. 
              Visit the <a href="/subscriptions" className="text-primary-600 hover:text-primary-500">Subscriptions</a> page to manage your subscription.
            </p>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default BusinessAccount;
