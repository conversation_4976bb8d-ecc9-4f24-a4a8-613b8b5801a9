# Schema Audit Summary

## Overview

This document summarizes the findings and fixes from the schema audit. The audit identified several issues with the database schema and model definitions, which have been addressed with migration files and documentation.

## Issues Identified

### 1. Duplicate Associations

Some models have associations defined both in the individual model files and in the central `associations.js` file. This can lead to duplicate associations at runtime, which might cause unexpected behavior.

**Examples:**
- `DocumentSigner.js`
- `DocumentSignature.js`
- `DocumentField.js`
- `DocumentAuditLog.js`

### 2. Inconsistent Association Definitions

Some models have their associations defined only in the individual model files, while others have them defined only in the central `associations.js` file. This inconsistency makes it harder to understand the full relationship structure of the database.

**Examples:**
- `ApiRequest.js`
- `CropDisease.js`
- `Task.js`

### 3. Data Type Inconsistencies

Some columns have inconsistent data types between the database schema and the model definitions.

**Examples:**
- `user_farms.created_at` and `user_farms.updated_at` are defined as `varchar` in the database schema, but as `DataTypes.DATE` in the model definition.
- `user_farms.permissions` is defined as `varchar` in the database schema, but as `DataTypes.JSON` in the model definition.

### 4. Enum Type Usage

Models are defining enum types inline rather than using the enum types defined in the database schema. This could lead to inconsistencies if the enum values change in the database but not in the model.

**Examples:**
- `DocumentSignature.js` defines `signature_type` as `DataTypes.ENUM('drawn', 'typed', 'uploaded', 'click')` instead of using the `enum_document_signatures_signature_type` enum type.
- `DocumentSigner.js` defines `status` as `DataTypes.ENUM('pending', 'sent', 'viewed', 'signed', 'declined')` instead of using the `enum_document_signers_status` enum type.

## Fixes Implemented

### 1. Data Type Inconsistencies

Created a migration file to fix the data type inconsistencies in the `user_farms` table:
- `fix_user_farms_column_types.sql`: Changes the data types of `created_at`, `updated_at`, and `permissions` columns in the `user_farms` table.

### 2. Association Definitions

Created a migration file to document the issues with association definitions and recommend an approach to fix them:
- `fix_association_definitions.sql`: Documents the code-level issues with association definitions in models.

## Recommended Next Steps

1. **Fix Association Definitions**: Follow the recommendations in `fix_association_definitions.sql` to move all associations to `associations.js` and remove duplicate associations from individual model files.

2. **Use Enum Types from Database Schema**: Update models to use the enum types defined in the database schema instead of defining them inline.

3. **Regular Schema Audits**: Conduct regular schema audits to identify and fix any new inconsistencies that may arise.

## Documentation

Detailed documentation for each fix has been provided in the following files:
- `README_user_farms_column_types_fix.md`: Documents the fix for data type inconsistencies in the `user_farms` table.
- `README_association_definitions_fix.md`: Documents the issues with association definitions and recommends an approach to fix them.
- `README_schema_audit_summary.md` (this file): Summarizes all findings and fixes from the schema audit.