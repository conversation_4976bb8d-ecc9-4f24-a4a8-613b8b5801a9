-- Add phone book integration columns to customers table
-- Set the search path to the appropriate schema
SET search_path TO site;

ALTER TABLE site.customers ADD COLUMN IF NOT EXISTS phone_book_subscription BOOLEAN DEFAULT FALSE;
COMMENT ON COLUMN site.customers.phone_book_subscription IS 'Whether the customer is subscribed to phone book updates';

ALTER TABLE site.customers ADD COLUMN IF NOT EXISTS ios_phone_book_sync BOOLEAN DEFAULT FALSE;
COMMENT ON COLUMN site.customers.ios_phone_book_sync IS 'Whether to sync with iOS phone book';

ALTER TABLE site.customers ADD COLUMN IF NOT EXISTS android_phone_book_sync BOOLEAN DEFAULT FALSE;
COMMENT ON COLUMN site.customers.android_phone_book_sync IS 'Whether to sync with Android phone book';

ALTER TABLE site.customers ADD COLUMN IF NOT EXISTS phone_book_last_sync TIMESTAMP;
COMMENT ON COLUMN site.customers.phone_book_last_sync IS 'When the customer was last synced with phone books';

ALTER TABLE site.customers ADD COLUMN IF NOT EXISTS phone_book_sync_id VARCHAR(255);
COMMENT ON COLUMN site.customers.phone_book_sync_id IS 'ID for tracking sync status';

ALTER TABLE site.customers ADD COLUMN IF NOT EXISTS external_phone_book_id VARCHAR(255);
COMMENT ON COLUMN site.customers.external_phone_book_id IS 'ID of the contact in external phone book systems';