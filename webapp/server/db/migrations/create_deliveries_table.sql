-- Create deliveries table for transport management

-- Set the search path to the site schema
SET search_path TO site;

-- Create deliveries table if it doesn't exist
CREATE TABLE IF NOT EXISTS site.deliveries (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  farm_id UUID NOT NULL REFERENCES site.farms(id),
  driver_id UUID REFERENCES site.drivers(id),
  customer_id UUID REFERENCES site.customers(id),
  order_id UUID REFERENCES site.orders(id),
  delivery_type VARCHAR(20) NOT NULL DEFAULT 'product',
  status VARCHAR(20) NOT NULL DEFAULT 'scheduled',
  scheduled_date TIMESTAMP WITH TIME ZONE NOT NULL,
  estimated_arrival TIMESTAMP WITH TIME ZONE,
  actual_delivery_date TIMESTAMP WITH TIME ZONE,
  delivery_address VARCHAR(255) NOT NULL,
  delivery_city VARCHAR(100) NOT NULL,
  delivery_state VARCHAR(50) NOT NULL,
  delivery_zip VARCHAR(20) NOT NULL,
  delivery_country VARCHAR(100) NOT NULL DEFAULT 'USA',
  delivery_instructions TEXT,
  signature_required BOOLEAN DEFAULT FALSE,
  signature_image VARCHAR(255),
  proof_of_delivery_image VARCHAR(255),
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Add enum constraint for delivery_type
ALTER TABLE site.deliveries DROP CONSTRAINT IF EXISTS deliveries_delivery_type_check;
ALTER TABLE site.deliveries ADD CONSTRAINT deliveries_delivery_type_check 
  CHECK (delivery_type IN ('product', 'equipment', 'other'));

-- Add enum constraint for status
ALTER TABLE site.deliveries DROP CONSTRAINT IF EXISTS deliveries_status_check;
ALTER TABLE site.deliveries ADD CONSTRAINT deliveries_status_check 
  CHECK (status IN ('scheduled', 'in_transit', 'delivered', 'failed', 'cancelled'));

-- Add comments to columns
COMMENT ON COLUMN site.deliveries.id IS 'Unique identifier for the delivery';
COMMENT ON COLUMN site.deliveries.farm_id IS 'ID of the farm this delivery belongs to';
COMMENT ON COLUMN site.deliveries.driver_id IS 'ID of the driver assigned to this delivery';
COMMENT ON COLUMN site.deliveries.customer_id IS 'ID of the customer receiving this delivery';
COMMENT ON COLUMN site.deliveries.order_id IS 'ID of the order associated with this delivery';
COMMENT ON COLUMN site.deliveries.delivery_type IS 'Type of delivery: product, equipment, or other';
COMMENT ON COLUMN site.deliveries.status IS 'Status of the delivery: scheduled, in_transit, delivered, failed, or cancelled';
COMMENT ON COLUMN site.deliveries.scheduled_date IS 'Scheduled date and time for the delivery';
COMMENT ON COLUMN site.deliveries.estimated_arrival IS 'Estimated arrival time';
COMMENT ON COLUMN site.deliveries.actual_delivery_date IS 'Actual date and time when delivery was completed';
COMMENT ON COLUMN site.deliveries.delivery_address IS 'Street address for delivery';
COMMENT ON COLUMN site.deliveries.delivery_city IS 'City for delivery';
COMMENT ON COLUMN site.deliveries.delivery_state IS 'State/province for delivery';
COMMENT ON COLUMN site.deliveries.delivery_zip IS 'ZIP/postal code for delivery';
COMMENT ON COLUMN site.deliveries.delivery_country IS 'Country for delivery';
COMMENT ON COLUMN site.deliveries.delivery_instructions IS 'Special instructions for the delivery';
COMMENT ON COLUMN site.deliveries.signature_required IS 'Whether a signature is required upon delivery';
COMMENT ON COLUMN site.deliveries.signature_image IS 'Path to the signature image file';
COMMENT ON COLUMN site.deliveries.proof_of_delivery_image IS 'Path to the proof of delivery image file';
COMMENT ON COLUMN site.deliveries.notes IS 'Additional notes about the delivery';
COMMENT ON COLUMN site.deliveries.created_at IS 'Timestamp when the delivery record was created';
COMMENT ON COLUMN site.deliveries.updated_at IS 'Timestamp when the delivery record was last updated';

-- Create indexes
CREATE INDEX IF NOT EXISTS deliveries_farm_id_idx ON site.deliveries(farm_id);
CREATE INDEX IF NOT EXISTS deliveries_driver_id_idx ON site.deliveries(driver_id);
CREATE INDEX IF NOT EXISTS deliveries_customer_id_idx ON site.deliveries(customer_id);
CREATE INDEX IF NOT EXISTS deliveries_order_id_idx ON site.deliveries(order_id);
CREATE INDEX IF NOT EXISTS deliveries_status_idx ON site.deliveries(status);
CREATE INDEX IF NOT EXISTS deliveries_scheduled_date_idx ON site.deliveries(scheduled_date);
CREATE INDEX IF NOT EXISTS deliveries_delivery_type_idx ON site.deliveries(delivery_type);

-- Verify the table was created
DO $$
DECLARE
    table_exists BOOLEAN;
BEGIN
    SELECT EXISTS (
        SELECT 1 
        FROM information_schema.tables 
        WHERE table_schema = 'site' 
        AND table_name = 'deliveries'
    ) INTO table_exists;

    IF table_exists THEN
        RAISE NOTICE 'The deliveries table was successfully created.';
    ELSE
        RAISE EXCEPTION 'Failed to create the deliveries table.';
    END IF;
END $$;

-- Create trigger for updated_at
CREATE OR REPLACE FUNCTION update_deliveries_updated_at()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = CURRENT_TIMESTAMP;
   RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_deliveries_timestamp
BEFORE UPDATE ON site.deliveries
FOR EACH ROW EXECUTE FUNCTION update_deliveries_updated_at();