-- Migration: Fix missing customer association for specific invoice
-- Depends on: add_payment_transaction_id_to_invoices.sql

SET search_path TO site;

-- This script fixes an invoice that is not properly associated with a customer
-- The URL in the issue description indicates:
-- Customer ID: e0b6455c-86e5-4310-8685-499003d4da7d
-- Farm ID: 4d9141d5-df79-4e3c-9423-ca342bd6cbcc

DO $$
DECLARE
    farm_id_var UUID := '4d9141d5-df79-4e3c-9423-ca342bd6cbcc';
    customer_id_var UUID := 'e0b6455c-86e5-4310-8685-499003d4da7d';
    invoice_id_var UUID;
    invoice_count INT;
BEGIN
    -- Verify the customer exists
    PERFORM id FROM customers WHERE id = customer_id_var AND farm_id = farm_id_var;

    IF NOT FOUND THEN
        RAISE NOTICE 'Customer with ID % not found for farm %.', customer_id_var, farm_id_var;
        RETURN;
    END IF;

    -- Find invoices for this farm that don't have a customer association
    SELECT COUNT(*) INTO invoice_count
    FROM invoices
    WHERE farm_id = farm_id_var
    AND customer_id IS NULL;

    RAISE NOTICE 'Found % invoices without customer association for farm %.', invoice_count, farm_id_var;

    -- Update all invoices for this farm that don't have a customer association
    UPDATE invoices
    SET customer_id = customer_id_var
    WHERE farm_id = farm_id_var
    AND customer_id IS NULL;

    GET DIAGNOSTICS invoice_count = ROW_COUNT;
    RAISE NOTICE 'Updated % invoices to associate with customer %.', invoice_count, customer_id_var;
END $$;
