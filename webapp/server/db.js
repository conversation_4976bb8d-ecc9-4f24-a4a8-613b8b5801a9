import { Pool } from 'pg';
import dotenv from 'dotenv';

dotenv.config();

// Create a connection pool with explicit max client setting
const pool = new Pool({
  host: process.env.DB_HOST,
  port: process.env.DB_PORT,
  database: process.env.DB_NAME,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
  max: process.env.DB_POOL_MAX ? parseInt(process.env.DB_POOL_MAX, 10) : 20, // Default to 20 if not specified
  idleTimeoutMillis: 30000, // Close idle clients after 30 seconds
  connectionTimeoutMillis: 2000, // Return an error after 2 seconds if connection could not be established
});

// Set the search_path to 'site' schema for all connections
pool.on('connect', (client) => {
  client.query('SET search_path TO site');
});

// Handle pool errors
pool.on('error', (err, client) => {
  console.error('Unexpected error on idle client', err);
});

// Export the query method for use in controllers
export default {
  // Simple query method that automatically handles connection acquisition/release
  query: (text, params) => pool.query(text, params),

  // Get a client from the pool to perform multiple operations in a transaction
  getClient: async () => {
    const client = await pool.connect();
    const originalRelease = client.release;

    // Override the release method to log when a client is returned to the pool
    client.release = () => {
      client.query('RESET ALL'); // Reset all session parameters
      originalRelease.apply(client);
    };

    return client;
  },

  // Execute a function with a dedicated client that is automatically released
  withClient: async (callback) => {
    const client = await pool.connect();
    try {
      return await callback(client);
    } finally {
      client.release();
    }
  }
};
