<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Delivery Scheduled Confirmation</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      margin: 0;
      padding: 0;
    }
    .container {
      max-width: 600px;
      margin: 0 auto;
      padding: 20px;
    }
    .header {
      background-color: #4CAF50;
      padding: 20px;
      text-align: center;
    }
    .header h1 {
      color: white;
      margin: 0;
      font-size: 24px;
    }
    .content {
      padding: 20px;
      background-color: #f9f9f9;
    }
    .footer {
      padding: 20px;
      text-align: center;
      font-size: 12px;
      color: #777;
    }
    .button {
      display: inline-block;
      background-color: #4CAF50;
      color: white;
      text-decoration: none;
      padding: 10px 20px;
      border-radius: 4px;
      margin-top: 20px;
    }
    .order-details {
      margin-top: 20px;
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 15px;
      background-color: white;
    }
    .delivery-details {
      margin-top: 20px;
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 15px;
      background-color: white;
    }
    .success-message {
      background-color: #dff0d8;
      border: 1px solid #d6e9c6;
      color: #3c763d;
      padding: 15px;
      border-radius: 4px;
      margin-bottom: 20px;
    }
    .calendar-icon {
      text-align: center;
      margin: 20px 0;
      font-size: 48px;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Delivery Scheduled</h1>
    </div>
    <div class="content">
      <div class="success-message">
        <p><strong>Success!</strong> Your delivery has been scheduled.</p>
      </div>
      
      <p>Hello {{customerName}},</p>
      
      <p>Your delivery from {{farmName}} has been scheduled. Please see the details below:</p>
      
      <div class="calendar-icon">
        📅
      </div>
      
      <div class="delivery-details">
        <p><strong>Delivery Date:</strong> {{deliveryDate}}</p>
        <p><strong>Delivery Time Window:</strong> {{deliveryTimeWindow}}</p>
        <p><strong>Delivery Address:</strong><br>
          {{deliveryAddress}}<br>
          {{deliveryCity}}, {{deliveryState}} {{deliveryZipCode}}
        </p>
        
        {{#if deliveryInstructions}}
        <p><strong>Delivery Instructions:</strong></p>
        <p>{{deliveryInstructions}}</p>
        {{/if}}
        
        {{#if driverTrackingEnabled}}
        <p><strong>Driver Tracking:</strong> Available on delivery day</p>
        {{/if}}
      </div>
      
      <p><strong>Order Details:</strong></p>
      <div class="order-details">
        <p><strong>Order ID:</strong> {{orderId}}</p>
        <p><strong>Items:</strong></p>
        {{#each items}}
        <p>{{quantity}} x {{productName}}</p>
        {{/each}}
      </div>
      
      <p>On the day of delivery, you will receive another email with tracking information when your order is on its way.</p>
      
      <p>If you need to make any changes to your delivery, please contact {{farmName}} as soon as possible.</p>
      
      <p>You can view your order details by clicking the button below:</p>
      
      <a href="{{orderUrl}}" class="button">View Order</a>
      
      <p>Thank you for your order!</p>
    </div>
    <div class="footer">
      <p>&copy; {{year}} NxtAcre. All rights reserved.</p>
      <p>This email was sent to {{email}}.</p>
    </div>
  </div>
</body>
</html>