import { sequelize } from '../config/database.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function addAllRequiredColumnsToFarms() {
  try {
    console.log('Starting to add all required columns to farms table...');
    
    // Read the SQL file
    const sqlFilePath = path.join(__dirname, '../db/add_all_required_columns_to_farms.sql');
    console.log(`Reading SQL file from: ${sqlFilePath}`);
    const sql = fs.readFileSync(sqlFilePath, 'utf8');
    
    // Execute the SQL
    console.log('Executing SQL...');
    await sequelize.query(sql);
    
    console.log('All required columns added successfully to farms table');
  } catch (error) {
    console.error('Error adding required columns:', error);
    process.exit(1);
  }
}

// Run the function
addAllRequiredColumnsToFarms()
  .then(() => {
    console.log('Migration completed, exiting...');
    process.exit(0);
  })
  .catch(err => {
    console.error('Unhandled error:', err);
    process.exit(1);
  });