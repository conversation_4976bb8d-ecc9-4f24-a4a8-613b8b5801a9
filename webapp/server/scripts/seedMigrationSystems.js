import { v4 as uuidv4 } from 'uuid';
import MigrationSystem from '../models/MigrationSystem.js';
import { sequelize } from '../config/database.js';
import { setupAssociations } from '../models/associations.js';

// Initialize database and associations
const initDatabase = async () => {
  try {
    // Test database connection
    await sequelize.authenticate();
    console.log('Database connection has been established successfully.');

    // Setup associations
    setupAssociations();

    // Sync models with database
    await sequelize.sync();
    console.log('Models synchronized with database.');
  } catch (error) {
    console.error('Unable to connect to the database:', error);
    process.exit(1);
  }
};

// Seed migration systems
const seedMigrationSystems = async () => {
  try {
    // Check if there are already migration systems in the database
    const existingSystems = await MigrationSystem.findAll();

    if (existingSystems.length > 0) {
      console.log(`Found ${existingSystems.length} existing migration systems. Skipping seed.`);
      return;
    }

    // Define migration systems to seed
    const migrationSystems = [
      {
        id: uuidv4(),
        name: 'John <PERSON> Operations Center',
        description: 'Import data from John Deere Operations Center including fields, equipment, and crop data.',
        logo_url: 'https://www.deere.com/assets/images/region-4/products/equipment/technology-products/operations-center/operations-center-icon.png',
        website: 'https://www.deere.com/en/technology-products/precision-ag-technology/operations-center/',
        supported_formats: ['csv', 'excel', 'json'],
        supported_entities: ['fields', 'equipment', 'crops', 'activities']
      },
      {
        id: uuidv4(),
        name: 'Climate FieldView',
        description: 'Import data from Climate FieldView including field boundaries, planting data, and yield data.',
        logo_url: 'https://climate.com/assets/img/logos/fieldview-logo.png',
        website: 'https://climate.com/',
        supported_formats: ['csv', 'excel', 'json'],
        supported_entities: ['fields', 'planting', 'harvest', 'weather']
      },
      {
        id: uuidv4(),
        name: 'Trimble Ag Software',
        description: 'Import data from Trimble Ag Software including farm records, field boundaries, and crop plans.',
        logo_url: 'https://agriculture.trimble.com/wp-content/uploads/2020/01/trimble-ag-software-icon.png',
        website: 'https://agriculture.trimble.com/software/',
        supported_formats: ['csv', 'excel'],
        supported_entities: ['farms', 'fields', 'crops', 'plans']
      },
      {
        id: uuidv4(),
        name: 'AgriWebb',
        description: 'Import livestock and grazing data from AgriWebb.',
        logo_url: 'https://www.agriwebb.com/wp-content/uploads/2020/01/agriwebb-logo.png',
        website: 'https://www.agriwebb.com/',
        supported_formats: ['csv', 'excel'],
        supported_entities: ['livestock', 'grazing', 'treatments']
      },
      {
        id: uuidv4(),
        name: 'Granular',
        description: 'Import farm management data from Granular including financials, crop plans, and inventory.',
        logo_url: 'https://granular.ag/wp-content/uploads/2020/01/granular-logo.png',
        website: 'https://granular.ag/',
        supported_formats: ['csv', 'excel', 'json'],
        supported_entities: ['farms', 'fields', 'crops', 'inventory', 'financials']
      },
      {
        id: uuidv4(),
        name: 'FarmLogs',
        description: 'Import field and crop data from FarmLogs.',
        logo_url: 'https://farmlogs.com/wp-content/uploads/2020/01/farmlogs-logo.png',
        website: 'https://farmlogs.com/',
        supported_formats: ['csv', 'excel'],
        supported_entities: ['fields', 'crops', 'activities', 'rainfall']
      },
      {
        id: uuidv4(),
        name: 'Conservis',
        description: 'Import farm management data from Conservis including inventory, activities, and financials.',
        logo_url: 'https://conservis.ag/wp-content/uploads/2020/01/conservis-logo.png',
        website: 'https://conservis.ag/',
        supported_formats: ['csv', 'excel'],
        supported_entities: ['inventory', 'activities', 'financials']
      },
      {
        id: uuidv4(),
        name: 'CSV Import',
        description: 'Import data from CSV files using NxtAcre templates.',
        logo_url: null,
        website: null,
        supported_formats: ['csv'],
        supported_entities: ['farms', 'fields', 'crops', 'livestock', 'equipment', 'inventory', 'employees']
      },
      {
        id: uuidv4(),
        name: 'Excel Import',
        description: 'Import data from Excel files using NxtAcre templates.',
        logo_url: null,
        website: null,
        supported_formats: ['excel'],
        supported_entities: ['farms', 'fields', 'crops', 'livestock', 'equipment', 'inventory', 'employees']
      }
    ];

    // Create migration systems in the database
    await MigrationSystem.bulkCreate(migrationSystems);

    console.log(`Successfully seeded ${migrationSystems.length} migration systems.`);
  } catch (error) {
    console.error('Error seeding migration systems:', error);
  }
};

// Run the script
const run = async () => {
  try {
    await initDatabase();
    await seedMigrationSystems();
    console.log('Seed completed successfully.');
    process.exit(0);
  } catch (error) {
    console.error('Error running seed script:', error);
    process.exit(1);
  }
};

run();
