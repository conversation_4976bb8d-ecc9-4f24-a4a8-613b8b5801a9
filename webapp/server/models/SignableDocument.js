import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import User from './User.js';
import Farm from './Farm.js';

const SignableDocument = defineModel('SignableDocument', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  title: {
    type: DataTypes.STRING(255),
    allowNull: false
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  document_type: {
    type: DataTypes.ENUM('agreement', 'contract', 'lease', 'invoice', 'receipt', 'other'),
    allowNull: false
  },
  status: {
    type: DataTypes.ENUM('draft', 'sent', 'viewed', 'signed', 'completed', 'declined', 'expired', 'canceled'),
    allowNull: false,
    defaultValue: 'draft'
  },
  file_path: {
    type: DataTypes.STRING(1024),
    allowNull: true
  },
  file_size: {
    type: DataTypes.BIGINT,
    allowNull: true
  },
  file_type: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  mime_type: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  version: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 1
  },
  is_template: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false
  },
  template_id: {
    type: DataTypes.UUID,
    allowNull: true
  },
  // tenant_id field removed as part of migration from tenant to farm
  farm_id: {
    type: DataTypes.UUID,
    allowNull: false, // Changed from true to false as farm_id is now required instead of tenant_id
    references: {
      model: Farm,
      key: 'id'
    }
  },
  created_by: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: User,
      key: 'id'
    }
  },
  completed_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  expires_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  has_blockchain_verification: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false
  },
  is_form_built: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'signable_documents',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      name: 'signable_documents_title_idx',
      fields: ['title']
    },
    {
      name: 'signable_documents_document_type_idx',
      fields: ['document_type']
    },
    {
      name: 'signable_documents_status_idx',
      fields: ['status']
    },
    // tenant_id index removed as part of migration from tenant to farm
    {
      name: 'signable_documents_farm_id_idx',
      fields: ['farm_id']
    },
    {
      name: 'signable_documents_created_by_idx',
      fields: ['created_by']
    },
    {
      name: 'signable_documents_template_id_idx',
      fields: ['template_id']
    }
  ]
});

// Define associations
// tenant association removed as part of migration from tenant to farm
// Self-referential association for templates
SignableDocument.belongsTo(SignableDocument, { foreignKey: 'template_id', as: 'template' });
SignableDocument.hasMany(SignableDocument, { foreignKey: 'template_id', as: 'derivedDocuments' });

// Other associations will be defined in associations.js after all models are created

// Association with DocumentElement will be added after DocumentElement model is imported in associations.js

export default SignableDocument;
