import matrixClientService from '../services/matrixClientService.js';
import MatrixRoomMapping from '../models/MatrixRoomMapping.js';
import ChatConversation from '../models/ChatConversation.js';
import { getNxtAcreUserIdToMatrixUserId, getMatrixUserIdToNxtAcreUserId } from '../utils/matrixUtils.js';

/**
 * Matrix Chat Controller
 * Handles chat-related operations using Matrix Synapse
 */
const matrixChatController = {
  /**
   * Get all conversations for the current user
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getUserConversations: async (req, res) => {
    try {
      const userId = req.user.id;
      const { farm_id } = req.query;

      // Get all rooms for the user from Matrix
      const rooms = await matrixClientService.getRooms(userId);

      // Transform Matrix rooms to NxtAcre conversation format
      const conversations = await Promise.all(rooms.map(async (room) => {
        // Check if this room has a mapping to a NxtAcre conversation
        const mapping = await MatrixRoomMapping.getByMatrixRoomId(room.roomId);

        // If there's a mapping, get the NxtAcre conversation
        if (mapping) {
          const conversation = await ChatConversation.findByPk(mapping.conversation_id);
          if (conversation) {
            // Return the NxtAcre conversation with additional Matrix data
            return {
              ...conversation.toJSON(),
              matrix_room_id: room.roomId
            };
          }
        }

        // If there's no mapping or the conversation doesn't exist,
        // create a conversation object from the Matrix room
        const roomState = room.currentState.getStateEvents('m.room.create', '');
        const creatorUserId = roomState ? roomState.getContent().creator : null;

        // Get the room type
        let type = 'group';
        if (room.getJoinRule() === 'public') {
          type = 'channel';
        } else if (room.getMembers().length === 2) {
          type = 'direct';
        }

        // Get the farm ID if available
        const farmIdEvent = room.currentState.getStateEvents('nxtacre.farm_id', '');
        const farmId = farmIdEvent ? farmIdEvent.getContent().farm_id : null;

        // Filter by farm_id if provided
        if (farm_id && farmId !== farm_id) {
          return null;
        }

        // Get participants
        const participants = room.getMembers()
          .filter(member => member.membership === 'join' || member.membership === 'invite')
          .map(member => {
            const matrixUserId = member.userId;
            // Extract NxtAcre user ID from Matrix user ID
            const nxtAcreUserId = matrixUserId.split(':')[0].substring(1);
            if (nxtAcreUserId.startsWith('nxtacre_')) {
              return {
                user_id: nxtAcreUserId.substring(8),
                display_name: member.name,
                avatar_url: member.getAvatarUrl(),
                is_admin: room.getPowerLevelContent().users[matrixUserId] >= 100
              };
            }
            return null;
          })
          .filter(Boolean);

        // Get unread count
        const unreadCount = room.getUnreadNotificationCount();

        return {
          id: room.roomId,
          name: room.name,
          type,
          farm_id: farmId,
          created_by: creatorUserId ? creatorUserId.split(':')[0].substring(1) : null,
          is_pinned: false, // Matrix doesn't have a concept of pinned rooms
          created_at: new Date(room.getCreationTs()).toISOString(),
          updated_at: new Date(room.getLastActiveTs()).toISOString(),
          participants,
          unread_count: unreadCount,
          matrix_room_id: room.roomId
        };
      }));

      // Filter out null values (rooms that didn't match the farm_id filter)
      const filteredConversations = conversations.filter(Boolean);

      return res.status(200).json(filteredConversations);
    } catch (error) {
      console.error('Error getting user conversations:', error);
      return res.status(500).json({ error: 'Failed to get conversations' });
    }
  },

  /**
   * Get a conversation by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getConversation: async (req, res) => {
    try {
      const { id } = req.params;
      const userId = req.user.id;

      // Check if this is a Matrix room ID or a NxtAcre conversation ID
      let roomId = id;
      if (!id.startsWith('!')) {
        // This is a NxtAcre conversation ID, get the Matrix room ID
        const mapping = await MatrixRoomMapping.getByConversationId(id);
        if (!mapping) {
          return res.status(404).json({ error: 'Conversation not found' });
        }
        roomId = mapping.matrix_room_id;
      }

      // Get the room from Matrix
      const room = await matrixClientService.getRoom(roomId, userId);
      if (!room) {
        return res.status(404).json({ error: 'Conversation not found' });
      }

      // Check if the user is a participant
      const matrixUserId = getNxtAcreUserIdToMatrixUserId(userId);
      const member = room.getMember(matrixUserId);
      if (!member || (member.membership !== 'join' && member.membership !== 'invite')) {
        return res.status(403).json({ error: 'You are not a participant in this conversation' });
      }

      // Get the room type
      let type = 'group';
      if (room.getJoinRule() === 'public') {
        type = 'channel';
      } else if (room.getMembers().length === 2) {
        type = 'direct';
      }

      // Get the farm ID if available
      const farmIdEvent = room.currentState.getStateEvents('nxtacre.farm_id', '');
      const farmId = farmIdEvent ? farmIdEvent.getContent().farm_id : null;

      // Get participants
      const participants = room.getMembers()
        .filter(member => member.membership === 'join' || member.membership === 'invite')
        .map(member => {
          const matrixUserId = member.userId;
          // Extract NxtAcre user ID from Matrix user ID
          const nxtAcreUserId = matrixUserId.split(':')[0].substring(1);
          if (nxtAcreUserId.startsWith('nxtacre_')) {
            return {
              user_id: nxtAcreUserId.substring(8),
              display_name: member.name,
              avatar_url: member.getAvatarUrl(),
              is_admin: room.getPowerLevelContent().users[matrixUserId] >= 100
            };
          }
          return null;
        })
        .filter(Boolean);

      // Get unread count
      const unreadCount = room.getUnreadNotificationCount();

      // Check if this room has a mapping to a NxtAcre conversation
      const mapping = await MatrixRoomMapping.getByMatrixRoomId(roomId);
      let conversationId = null;

      if (mapping) {
        conversationId = mapping.conversation_id;
      }

      const conversation = {
        id: conversationId || roomId,
        name: room.name,
        type,
        farm_id: farmId,
        created_by: room.getCreatorUserId() ? room.getCreatorUserId().split(':')[0].substring(1) : null,
        is_pinned: false, // Matrix doesn't have a concept of pinned rooms
        created_at: new Date(room.getCreationTs()).toISOString(),
        updated_at: new Date(room.getLastActiveTs()).toISOString(),
        participants,
        unread_count: unreadCount,
        matrix_room_id: roomId
      };

      return res.status(200).json(conversation);
    } catch (error) {
      console.error('Error getting conversation:', error);
      return res.status(500).json({ error: 'Failed to get conversation' });
    }
  },

  /**
   * Create a new conversation
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  createConversation: async (req, res) => {
    try {
      const { name, type, farm_id, participant_ids, associated_farm_id } = req.body;
      const userId = req.user.id;

      // Validate participant_ids
      if (!participant_ids || !Array.isArray(participant_ids) || participant_ids.length === 0) {
        return res.status(400).json({ error: 'At least one participant is required' });
      }

      // Check if this is a conversation with an associated farm
      if (associated_farm_id) {
        // Import required models
        const ChatPermission = (await import('../models/ChatPermission.js')).default;
        const FarmAssociation = (await import('../models/FarmAssociation.js')).default;
        const UserFarm = (await import('../models/UserFarm.js')).default;

        // Get the user's farm
        const userFarm = await UserFarm.findOne({
          where: {
            user_id: userId,
            farm_id: farm_id
          }
        });

        if (!userFarm) {
          return res.status(403).json({ error: 'You are not a member of this farm' });
        }

        // Check if the farms are associated
        const areAssociated = await FarmAssociation.areFarmsAssociated(farm_id, associated_farm_id);
        if (!areAssociated) {
          return res.status(403).json({ error: 'The farms are not associated' });
        }

        // Check if the user has permission to chat with associated farms
        const canChatWithAssociatedFarms = await ChatPermission.canChatWithAssociatedFarms(userId, farm_id);
        if (!canChatWithAssociatedFarms) {
          return res.status(403).json({ error: 'You do not have permission to chat with associated farms' });
        }
      }

      // For direct messages, check if conversation already exists
      if (type === 'direct' && participant_ids.length === 1) {
        // Get all rooms for the user
        const rooms = await matrixClientService.getRooms(userId);

        // Find a direct message room with the participant
        const participantMatrixUserId = getNxtAcreUserIdToMatrixUserId(participant_ids[0]);
        const existingRoom = rooms.find(room => {
          // Check if this is a direct message room
          if (room.getMembers().length !== 2) {
            return false;
          }

          // Check if the participant is in the room
          const member = room.getMember(participantMatrixUserId);
          return member && (member.membership === 'join' || member.membership === 'invite');
        });

        if (existingRoom) {
          // Get the conversation details
          const conversation = await this.getConversationFromRoom(existingRoom, userId);
          return res.status(200).json(conversation);
        }
      }

      // Create a room in Matrix
      const isPublic = type === 'channel';
      const response = await matrixClientService.createRoom(
        name,
        '', // No topic for now
        isPublic,
        userId,
        farm_id
      );

      const roomId = response.room_id;

      // Create a set of participant IDs to avoid duplicates
      const participantSet = new Set(participant_ids);

      // If farm_id is provided and this is not a direct message, add all farm users
      if (farm_id && type !== 'direct') {
        try {
          // Import UserFarm model
          const UserFarm = (await import('../models/UserFarm.js')).default;

          // Get all approved users for this farm
          const farmUsers = await UserFarm.findAll({
            where: {
              farm_id: farm_id,
              is_approved: true
            },
            attributes: ['user_id']
          });

          // Add farm users to the participant set
          for (const farmUser of farmUsers) {
            participantSet.add(farmUser.user_id);
          }

          console.log(`Added ${farmUsers.length} farm users to Matrix chat room ${roomId}`);
        } catch (error) {
          console.error(`Error adding farm users to room ${roomId}:`, error.message);
        }
      }

      // Invite all participants to the room
      for (const participantId of participantSet) {
        try {
          await matrixClientService.inviteToRoom(roomId, participantId);
        } catch (error) {
          console.error(`Error inviting user ${participantId} to room ${roomId}:`, error.message);
        }
      }

      // Get the room
      const room = await matrixClientService.getRoom(roomId, userId);

      // Create a conversation object from the Matrix room
      const conversation = await this.getConversationFromRoom(room, userId);

      return res.status(201).json(conversation);
    } catch (error) {
      console.error('Error creating conversation:', error);
      return res.status(500).json({ error: 'Failed to create conversation' });
    }
  },

  /**
   * Get messages for a conversation
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getMessages: async (req, res) => {
    try {
      const { id } = req.params;
      const { limit = 50, from = null } = req.query;
      const userId = req.user.id;

      // Check if this is a Matrix room ID or a NxtAcre conversation ID
      let roomId = id;
      if (!id.startsWith('!')) {
        // This is a NxtAcre conversation ID, get the Matrix room ID
        const mapping = await MatrixRoomMapping.getByConversationId(id);
        if (!mapping) {
          return res.status(404).json({ error: 'Conversation not found' });
        }
        roomId = mapping.matrix_room_id;
      }

      // Get messages from Matrix
      const response = await matrixClientService.getMessages(roomId, userId, from, parseInt(limit, 10));

      // Transform Matrix events to NxtAcre message format
      const messages = response.chunk.map(event => {
        // Only include message events
        if (event.type !== 'm.room.message') {
          return null;
        }

        const sender = event.sender;
        // Extract NxtAcre user ID from Matrix user ID
        const senderId = sender.split(':')[0].substring(1);
        if (!senderId.startsWith('nxtacre_')) {
          return null;
        }

        const nxtAcreSenderId = senderId.substring(8);

        return {
          id: event.event_id,
          conversation_id: id,
          sender_id: nxtAcreSenderId,
          message_type: 'text', // Matrix doesn't have a direct mapping to our message types
          content: event.content.body,
          is_edited: event.unsigned && event.unsigned.prev_content ? true : false,
          created_at: new Date(event.origin_server_ts).toISOString(),
          updated_at: new Date(event.origin_server_ts).toISOString(),
          matrix_event_id: event.event_id
        };
      }).filter(Boolean);

      return res.status(200).json({
        messages,
        next_batch: response.end,
        prev_batch: response.start
      });
    } catch (error) {
      console.error('Error getting messages:', error);
      return res.status(500).json({ error: 'Failed to get messages' });
    }
  },

  /**
   * Send a message
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  sendMessage: async (req, res) => {
    try {
      const { id } = req.params;
      const { content, message_type = 'text' } = req.body;
      const userId = req.user.id;

      // Check if this is a Matrix room ID or a NxtAcre conversation ID
      let roomId = id;
      if (!id.startsWith('!')) {
        // This is a NxtAcre conversation ID, get the Matrix room ID
        const mapping = await MatrixRoomMapping.getByConversationId(id);
        if (!mapping) {
          return res.status(404).json({ error: 'Conversation not found' });
        }
        roomId = mapping.matrix_room_id;
      }

      // Send the message to Matrix
      const response = await matrixClientService.sendMessage(roomId, content, 'm.room.message', userId);

      // Create a message object
      const message = {
        id: response.event_id,
        conversation_id: id,
        sender_id: userId,
        message_type,
        content,
        is_edited: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        matrix_event_id: response.event_id
      };

      return res.status(201).json(message);
    } catch (error) {
      console.error('Error sending message:', error);
      return res.status(500).json({ error: 'Failed to send message' });
    }
  },

  /**
   * Helper method to create a conversation object from a Matrix room
   * @param {Object} room - Matrix room
   * @param {string} userId - NxtAcre user ID
   * @returns {Object} Conversation object
   */
  getConversationFromRoom: async (room, userId) => {
    // Get the room type
    let type = 'group';
    if (room.getJoinRule() === 'public') {
      type = 'channel';
    } else if (room.getMembers().length === 2) {
      type = 'direct';
    }

    // Get the farm ID if available
    const farmIdEvent = room.currentState.getStateEvents('nxtacre.farm_id', '');
    const farmId = farmIdEvent ? farmIdEvent.getContent().farm_id : null;

    // Get participants
    const participants = room.getMembers()
      .filter(member => member.membership === 'join' || member.membership === 'invite')
      .map(member => {
        const matrixUserId = member.userId;
        // Extract NxtAcre user ID from Matrix user ID
        const nxtAcreUserId = matrixUserId.split(':')[0].substring(1);
        if (nxtAcreUserId.startsWith('nxtacre_')) {
          return {
            user_id: nxtAcreUserId.substring(8),
            display_name: member.name,
            avatar_url: member.getAvatarUrl(),
            is_admin: room.getPowerLevelContent().users[matrixUserId] >= 100
          };
        }
        return null;
      })
      .filter(Boolean);

    // Get unread count
    const unreadCount = room.getUnreadNotificationCount();

    // Check if this room has a mapping to a NxtAcre conversation
    const mapping = await MatrixRoomMapping.getByMatrixRoomId(room.roomId);
    let conversationId = null;

    if (mapping) {
      conversationId = mapping.conversation_id;
    }

    return {
      id: conversationId || room.roomId,
      name: room.name,
      type,
      farm_id: farmId,
      created_by: room.getCreatorUserId() ? room.getCreatorUserId().split(':')[0].substring(1) : null,
      is_pinned: false, // Matrix doesn't have a concept of pinned rooms
      created_at: new Date(room.getCreationTs()).toISOString(),
      updated_at: new Date(room.getLastActiveTs()).toISOString(),
      participants,
      unread_count: unreadCount,
      matrix_room_id: room.roomId
    };
  },

  /**
   * Get associated farms that the user can chat with
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getChattableAssociatedFarms: async (req, res) => {
    try {
      const userId = req.user.id;
      const { farm_id } = req.query;

      if (!farm_id) {
        return res.status(400).json({ error: 'Farm ID is required' });
      }

      // Import required models
      const ChatPermission = (await import('../models/ChatPermission.js')).default;
      const db = (await import('../db.js')).default;
      const UserFarm = (await import('../models/UserFarm.js')).default;

      // Check if the user is a member of the farm
      const userFarm = await UserFarm.findOne({
        where: {
          user_id: userId,
          farm_id: farm_id
        }
      });

      if (!userFarm) {
        return res.status(403).json({ error: 'You are not a member of this farm' });
      }

      // Check if the user has permission to chat with associated farms
      const canChatWithAssociatedFarms = await ChatPermission.canChatWithAssociatedFarms(userId, farm_id);
      if (!canChatWithAssociatedFarms) {
        return res.status(200).json({ farms: [] });
      }

      // Get all active farm associations for this farm
      const query = `
        SELECT 
          f.id, 
          f.name, 
          f.description,
          f.logo_url,
          fa.association_type,
          fa.status
        FROM farm_associations fa
        JOIN farms f ON (
          CASE 
            WHEN fa.initiator_farm_id = $1 THEN fa.associated_farm_id = f.id
            WHEN fa.associated_farm_id = $1 THEN fa.initiator_farm_id = f.id
          END
        )
        WHERE (fa.initiator_farm_id = $1 OR fa.associated_farm_id = $1)
        AND fa.status = 'active'
      `;

      const { rows: associatedFarms } = await db.query(query, [farm_id]);

      return res.status(200).json({ farms: associatedFarms });
    } catch (error) {
      console.error('Error getting chattable associated farms:', error);
      return res.status(500).json({ error: 'Failed to get associated farms' });
    }
  },

  /**
   * Get Matrix users for a farm
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getMatrixUsers: async (req, res) => {
    try {
      const userId = req.user.id;
      const { farm_id, include_associated = false } = req.query;

      if (!farm_id) {
        return res.status(400).json({ error: 'Farm ID is required' });
      }

      // Import required models
      const UserFarm = (await import('../models/UserFarm.js')).default;

      // Check if the user is a member of the farm
      const userFarm = await UserFarm.findOne({
        where: {
          user_id: userId,
          farm_id: farm_id
        }
      });

      if (!userFarm) {
        return res.status(403).json({ error: 'You are not a member of this farm' });
      }

      // Get Matrix users for the farm
      const farmUsers = await matrixClientService.getMatrixUsersForFarm(farm_id);

      // If include_associated is true, also get users from associated farms
      let associatedFarmUsers = [];
      if (include_associated === 'true') {
        // Check if the user has permission to chat with associated farms
        const ChatPermission = (await import('../models/ChatPermission.js')).default;
        const canChatWithAssociatedFarms = await ChatPermission.canChatWithAssociatedFarms(userId, farm_id);

        if (canChatWithAssociatedFarms) {
          associatedFarmUsers = await matrixClientService.getMatrixUsersForAssociatedFarms(farm_id);
        }
      }

      // Combine farm users and associated farm users, removing duplicates
      const allUsers = [...farmUsers];

      // Add associated farm users, avoiding duplicates
      for (const associatedUser of associatedFarmUsers) {
        if (!allUsers.some(user => user.name === associatedUser.name)) {
          allUsers.push(associatedUser);
        }
      }

      return res.status(200).json({
        users: allUsers,
        farm_id: farm_id
      });
    } catch (error) {
      console.error('Error getting Matrix users:', error);
      return res.status(500).json({ error: 'Failed to get Matrix users' });
    }
  }
};

export default matrixChatController;
