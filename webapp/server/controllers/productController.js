import Product from '../models/Product.js';
import Crop from '../models/Crop.js';
import Livestock from '../models/Livestock.js';
import Equipment from '../models/Equipment.js';
import { sequelize } from '../config/database.js';

// Get all products for a farm using query parameter
export const getFarmProductsByQuery = async (req, res) => {
  try {
    const { farmId } = req.query;

    if (!farmId) {
      return res.status(400).json({ 
        message: "Farm ID is required",
        errorType: "ValidationError",
        errorCode: "400",
        context: {
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    const products = await Product.findAll({
      where: { farm_id: farmId },
      order: [['name', 'ASC']]
    });

    res.status(200).json(products);
  } catch (error) {
    console.error('Error fetching farm products by query:', error);
    res.status(500).json({ 
      message: "Failed to fetch products",
      errorType: "ServerError",
      errorCode: "500",
      context: {
        url: req.originalUrl,
        method: req.method
      }
    });
  }
};

// Get all products for a farm
export const getFarmProducts = async (req, res) => {
  try {
    const { farmId } = req.params;

    if (!farmId) {
      return res.status(400).json({ 
        message: "Farm ID is required",
        errorType: "ValidationError",
        errorCode: "400",
        context: {
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    const products = await Product.findAll({
      where: { farm_id: farmId },
      order: [['name', 'ASC']]
    });

    res.status(200).json(products);
  } catch (error) {
    console.error('Error fetching farm products:', error);
    res.status(500).json({ 
      message: "Failed to fetch products",
      errorType: "ServerError",
      errorCode: "500",
      context: {
        url: req.originalUrl,
        method: req.method,
        farmId: req.params.farmId
      }
    });
  }
};

// Get a single product by ID
export const getProductById = async (req, res) => {
  try {
    const { productId } = req.params;

    const product = await Product.findByPk(productId);

    if (!product) {
      return res.status(404).json({ error: 'Product not found' });
    }

    res.status(200).json(product);
  } catch (error) {
    console.error('Error fetching product:', error);
    res.status(500).json({ error: 'Failed to fetch product' });
  }
};

// Create a new product
export const createProduct = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { 
      farm_id, 
      name, 
      description, 
      sku, 
      price, 
      cost, 
      unit, 
      category, 
      type, 
      source_id,
      is_active,
      is_tax_exempt
    } = req.body;

    // Validate required fields
    if (!farm_id || !name || !price) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Farm ID, name, and price are required' });
    }

    // Validate source_id if type is specified
    if (type && type !== 'other' && source_id) {
      let sourceExists = false;

      if (type === 'crop') {
        const crop = await Crop.findByPk(source_id);
        sourceExists = !!crop;
      } else if (type === 'livestock') {
        const livestock = await Livestock.findByPk(source_id);
        sourceExists = !!livestock;
      } else if (type === 'equipment') {
        const equipment = await Equipment.findByPk(source_id);
        sourceExists = !!equipment;
      }

      if (!sourceExists) {
        await transaction.rollback();
        return res.status(400).json({ error: `${type} with ID ${source_id} not found` });
      }
    }

    const product = await Product.create({
      farm_id,
      name,
      description,
      sku,
      price,
      cost,
      unit,
      category,
      type: type || 'other',
      source_id,
      is_active: is_active !== undefined ? is_active : true,
      is_tax_exempt: is_tax_exempt !== undefined ? is_tax_exempt : false
    }, { transaction });

    await transaction.commit();
    res.status(201).json(product);
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating product:', error);
    res.status(500).json({ error: 'Failed to create product' });
  }
};

// Update a product
export const updateProduct = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { productId } = req.params;
    const { 
      name, 
      description, 
      sku, 
      price, 
      cost, 
      unit, 
      category, 
      type, 
      source_id,
      is_active,
      is_tax_exempt
    } = req.body;

    const product = await Product.findByPk(productId);

    if (!product) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Product not found' });
    }

    // Validate source_id if type is specified
    if (type && type !== 'other' && source_id) {
      let sourceExists = false;

      if (type === 'crop') {
        const crop = await Crop.findByPk(source_id);
        sourceExists = !!crop;
      } else if (type === 'livestock') {
        const livestock = await Livestock.findByPk(source_id);
        sourceExists = !!livestock;
      } else if (type === 'equipment') {
        const equipment = await Equipment.findByPk(source_id);
        sourceExists = !!equipment;
      }

      if (!sourceExists) {
        await transaction.rollback();
        return res.status(400).json({ error: `${type} with ID ${source_id} not found` });
      }
    }

    await product.update({
      name: name || product.name,
      description: description !== undefined ? description : product.description,
      sku: sku !== undefined ? sku : product.sku,
      price: price !== undefined ? price : product.price,
      cost: cost !== undefined ? cost : product.cost,
      unit: unit !== undefined ? unit : product.unit,
      category: category !== undefined ? category : product.category,
      type: type || product.type,
      source_id: source_id !== undefined ? source_id : product.source_id,
      is_active: is_active !== undefined ? is_active : product.is_active,
      is_tax_exempt: is_tax_exempt !== undefined ? is_tax_exempt : product.is_tax_exempt
    }, { transaction });

    await transaction.commit();
    res.status(200).json(product);
  } catch (error) {
    await transaction.rollback();
    console.error('Error updating product:', error);
    res.status(500).json({ error: 'Failed to update product' });
  }
};

// Delete a product
export const deleteProduct = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { productId } = req.params;

    const product = await Product.findByPk(productId);

    if (!product) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Product not found' });
    }

    await product.destroy({ transaction });

    await transaction.commit();
    res.status(200).json({ message: 'Product deleted successfully' });
  } catch (error) {
    await transaction.rollback();
    console.error('Error deleting product:', error);
    res.status(500).json({ error: 'Failed to delete product' });
  }
};

// Create a product from a crop
export const createProductFromCrop = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { cropId } = req.params;
    const { price, cost, unit, is_active, is_tax_exempt } = req.body;

    const crop = await Crop.findByPk(cropId);

    if (!crop) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Crop not found' });
    }

    // Create a product based on the crop
    const product = await Product.create({
      farm_id: crop.farm_id,
      name: crop.name,
      description: `${crop.name} ${crop.variety ? `- ${crop.variety}` : ''} (${crop.year || 'Unknown Year'})`,
      price: price || 0,
      cost: cost,
      unit: unit,
      category: 'Crops',
      type: 'crop',
      source_id: crop.id,
      is_active: is_active !== undefined ? is_active : true,
      is_tax_exempt: is_tax_exempt !== undefined ? is_tax_exempt : false
    }, { transaction });

    await transaction.commit();
    res.status(201).json(product);
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating product from crop:', error);
    res.status(500).json({ error: 'Failed to create product from crop' });
  }
};

// Create a product from livestock
export const createProductFromLivestock = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { livestockId } = req.params;
    const { price, cost, unit, is_active, is_tax_exempt } = req.body;

    const livestock = await Livestock.findByPk(livestockId);

    if (!livestock) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Livestock not found' });
    }

    // Create a product based on the livestock
    const product = await Product.create({
      farm_id: livestock.farm_id,
      name: `${livestock.type} ${livestock.breed ? `- ${livestock.breed}` : ''}`,
      description: `${livestock.type} ${livestock.breed ? `- ${livestock.breed}` : ''} (Quantity: ${livestock.quantity})`,
      price: price || 0,
      cost: cost || livestock.acquisition_cost,
      unit: unit,
      category: 'Livestock',
      type: 'livestock',
      source_id: livestock.id,
      is_active: is_active !== undefined ? is_active : true,
      is_tax_exempt: is_tax_exempt !== undefined ? is_tax_exempt : false
    }, { transaction });

    await transaction.commit();
    res.status(201).json(product);
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating product from livestock:', error);
    res.status(500).json({ error: 'Failed to create product from livestock' });
  }
};

// Create a product from equipment
export const createProductFromEquipment = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { equipmentId } = req.params;
    const { price, cost, unit, is_active, is_tax_exempt } = req.body;

    const equipment = await Equipment.findByPk(equipmentId);

    if (!equipment) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Equipment not found' });
    }

    // Create a product based on the equipment
    const product = await Product.create({
      farm_id: equipment.farm_id,
      name: equipment.name,
      description: `${equipment.name} ${equipment.manufacturer ? `- ${equipment.manufacturer}` : ''} ${equipment.model ? `(${equipment.model})` : ''}`,
      price: price || 0,
      cost: cost || equipment.purchase_cost,
      unit: unit,
      category: 'Equipment',
      type: 'equipment',
      source_id: equipment.id,
      is_active: is_active !== undefined ? is_active : true,
      is_tax_exempt: is_tax_exempt !== undefined ? is_tax_exempt : false
    }, { transaction });

    await transaction.commit();
    res.status(201).json(product);
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating product from equipment:', error);
    res.status(500).json({ error: 'Failed to create product from equipment' });
  }
};
