import { sequelize } from '../config/database.js';
import Farm from '../models/Farm.js';
import Product from '../models/Product.js';
import { Op } from 'sequelize';

// Get farm fulfillment options
export const getFarmFulfillmentOptions = async (req, res) => {
  try {
    const { farmId } = req.params;

    // Check if farm exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({
        message: "Farm not found",
        errorType: "NotFound",
        errorCode: "404",
        context: {
          farmId,
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    // Get fulfillment options for the farm
    const fulfillmentOptions = await sequelize.models.farm_fulfillment_options.findOne({
      where: { farm_id: farmId }
    });

    // If no options exist yet, return default values
    if (!fulfillmentOptions) {
      return res.status(200).json({
        farm_id: farmId,
        offers_delivery: true,
        offers_pickup: true,
        delivery_fee: 0.00,
        min_order_for_free_delivery: null,
        delivery_radius_miles: null,
        pickup_instructions: null,
        delivery_instructions: null
      });
    }

    res.status(200).json(fulfillmentOptions);
  } catch (error) {
    console.error('Error fetching farm fulfillment options:', error);
    res.status(500).json({
      message: "Failed to fetch farm fulfillment options",
      errorType: "ServerError",
      errorCode: "500",
      context: {
        url: req.originalUrl,
        method: req.method
      }
    });
  }
};

// Update farm fulfillment options
export const updateFarmFulfillmentOptions = async (req, res) => {
  try {
    const { farmId } = req.params;
    const {
      offers_delivery,
      offers_pickup,
      delivery_fee,
      min_order_for_free_delivery,
      delivery_radius_miles,
      pickup_instructions,
      delivery_instructions
    } = req.body;

    // Check if farm exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({
        message: "Farm not found",
        errorType: "NotFound",
        errorCode: "404",
        context: {
          farmId,
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    // Check if user has permission to update farm settings
    const userFarm = await sequelize.models.user_farms.findOne({
      where: {
        user_id: req.user.id,
        farm_id: farmId,
        role: { [Op.in]: ['owner', 'admin'] }
      }
    });

    if (!userFarm) {
      return res.status(403).json({
        message: "You don't have permission to update this farm's fulfillment options",
        errorType: "Forbidden",
        errorCode: "403",
        context: {
          farmId,
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    // Update or create fulfillment options
    const [fulfillmentOptions, created] = await sequelize.models.farm_fulfillment_options.findOrCreate({
      where: { farm_id: farmId },
      defaults: {
        farm_id: farmId,
        offers_delivery: offers_delivery !== undefined ? offers_delivery : true,
        offers_pickup: offers_pickup !== undefined ? offers_pickup : true,
        delivery_fee: delivery_fee !== undefined ? delivery_fee : 0.00,
        min_order_for_free_delivery,
        delivery_radius_miles,
        pickup_instructions,
        delivery_instructions
      }
    });

    if (!created) {
      // Update existing record
      await fulfillmentOptions.update({
        offers_delivery: offers_delivery !== undefined ? offers_delivery : fulfillmentOptions.offers_delivery,
        offers_pickup: offers_pickup !== undefined ? offers_pickup : fulfillmentOptions.offers_pickup,
        delivery_fee: delivery_fee !== undefined ? delivery_fee : fulfillmentOptions.delivery_fee,
        min_order_for_free_delivery: min_order_for_free_delivery !== undefined ? min_order_for_free_delivery : fulfillmentOptions.min_order_for_free_delivery,
        delivery_radius_miles: delivery_radius_miles !== undefined ? delivery_radius_miles : fulfillmentOptions.delivery_radius_miles,
        pickup_instructions: pickup_instructions !== undefined ? pickup_instructions : fulfillmentOptions.pickup_instructions,
        delivery_instructions: delivery_instructions !== undefined ? delivery_instructions : fulfillmentOptions.delivery_instructions
      });
    }

    // Fetch the updated record
    const updatedOptions = await sequelize.models.farm_fulfillment_options.findOne({
      where: { farm_id: farmId }
    });

    res.status(200).json(updatedOptions);
  } catch (error) {
    console.error('Error updating farm fulfillment options:', error);
    res.status(500).json({
      message: "Failed to update farm fulfillment options",
      errorType: "ServerError",
      errorCode: "500",
      context: {
        url: req.originalUrl,
        method: req.method
      }
    });
  }
};

// Get product-specific fulfillment options
export const getProductFulfillmentOptions = async (req, res) => {
  try {
    const { productId } = req.params;

    // Check if product exists
    const product = await Product.findByPk(productId);
    if (!product) {
      return res.status(404).json({
        message: "Product not found",
        errorType: "NotFound",
        errorCode: "404",
        context: {
          productId,
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    // Get farm fulfillment options
    const farmFulfillmentOptions = await sequelize.models.farm_fulfillment_options.findOne({
      where: { farm_id: product.farm_id }
    });

    // Prepare response
    const response = {
      product_id: productId,
      farm_id: product.farm_id,
      override_farm_fulfillment: product.override_farm_fulfillment || false,
      offers_delivery: product.override_farm_fulfillment ? product.offers_delivery : (farmFulfillmentOptions?.offers_delivery || true),
      offers_pickup: product.override_farm_fulfillment ? product.offers_pickup : (farmFulfillmentOptions?.offers_pickup || true),
      farm_fulfillment_options: farmFulfillmentOptions || {
        offers_delivery: true,
        offers_pickup: true,
        delivery_fee: 0.00,
        min_order_for_free_delivery: null,
        delivery_radius_miles: null,
        pickup_instructions: null,
        delivery_instructions: null
      }
    };

    res.status(200).json(response);
  } catch (error) {
    console.error('Error fetching product fulfillment options:', error);
    res.status(500).json({
      message: "Failed to fetch product fulfillment options",
      errorType: "ServerError",
      errorCode: "500",
      context: {
        url: req.originalUrl,
        method: req.method
      }
    });
  }
};

// Update product-specific fulfillment options
export const updateProductFulfillmentOptions = async (req, res) => {
  try {
    const { productId } = req.params;
    const {
      override_farm_fulfillment,
      offers_delivery,
      offers_pickup
    } = req.body;

    // Check if product exists
    const product = await Product.findByPk(productId);
    if (!product) {
      return res.status(404).json({
        message: "Product not found",
        errorType: "NotFound",
        errorCode: "404",
        context: {
          productId,
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    // Check if user has permission to update product
    const userFarm = await sequelize.models.user_farms.findOne({
      where: {
        user_id: req.user.id,
        farm_id: product.farm_id,
        role: { [Op.in]: ['owner', 'admin', 'manager'] }
      }
    });

    if (!userFarm) {
      return res.status(403).json({
        message: "You don't have permission to update this product's fulfillment options",
        errorType: "Forbidden",
        errorCode: "403",
        context: {
          productId,
          farmId: product.farm_id,
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    // Update product fulfillment options
    await product.update({
      override_farm_fulfillment: override_farm_fulfillment !== undefined ? override_farm_fulfillment : product.override_farm_fulfillment,
      offers_delivery: offers_delivery !== undefined ? offers_delivery : product.offers_delivery,
      offers_pickup: offers_pickup !== undefined ? offers_pickup : product.offers_pickup
    });

    // Get farm fulfillment options for response
    const farmFulfillmentOptions = await sequelize.models.farm_fulfillment_options.findOne({
      where: { farm_id: product.farm_id }
    });

    // Prepare response
    const response = {
      product_id: productId,
      farm_id: product.farm_id,
      override_farm_fulfillment: product.override_farm_fulfillment,
      offers_delivery: product.override_farm_fulfillment ? product.offers_delivery : (farmFulfillmentOptions?.offers_delivery || true),
      offers_pickup: product.override_farm_fulfillment ? product.offers_pickup : (farmFulfillmentOptions?.offers_pickup || true),
      farm_fulfillment_options: farmFulfillmentOptions || {
        offers_delivery: true,
        offers_pickup: true,
        delivery_fee: 0.00,
        min_order_for_free_delivery: null,
        delivery_radius_miles: null,
        pickup_instructions: null,
        delivery_instructions: null
      }
    };

    res.status(200).json(response);
  } catch (error) {
    console.error('Error updating product fulfillment options:', error);
    res.status(500).json({
      message: "Failed to update product fulfillment options",
      errorType: "ServerError",
      errorCode: "500",
      context: {
        url: req.originalUrl,
        method: req.method
      }
    });
  }
};

// Get farm fulfillment options for marketplace
export const getMarketplaceFarmFulfillmentOptions = async (req, res) => {
  try {
    const { farmId } = req.params;

    // Check if farm exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({
        message: "Farm not found",
        errorType: "NotFound",
        errorCode: "404",
        context: {
          farmId,
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    // Get fulfillment options for the farm
    const fulfillmentOptions = await sequelize.models.farm_fulfillment_options.findOne({
      where: { farm_id: farmId }
    });

    // Prepare customer-facing response (omitting internal fields)
    const response = {
      farm_id: farmId,
      farm_name: farm.name,
      offers_delivery: fulfillmentOptions?.offers_delivery || true,
      offers_pickup: fulfillmentOptions?.offers_pickup || true,
      delivery_fee: fulfillmentOptions?.delivery_fee || 0.00,
      min_order_for_free_delivery: fulfillmentOptions?.min_order_for_free_delivery || null,
      delivery_radius_miles: fulfillmentOptions?.delivery_radius_miles || null,
      pickup_instructions: fulfillmentOptions?.pickup_instructions || null,
      delivery_instructions: fulfillmentOptions?.delivery_instructions || null
    };

    res.status(200).json(response);
  } catch (error) {
    console.error('Error fetching marketplace farm fulfillment options:', error);
    res.status(500).json({
      message: "Failed to fetch farm fulfillment options",
      errorType: "ServerError",
      errorCode: "500",
      context: {
        url: req.originalUrl,
        method: req.method
      }
    });
  }
};

// Set fulfillment method for cart
export const setCartFulfillmentMethod = async (req, res) => {
  try {
    const { 
      fulfillment_method, 
      delivery_address_id, 
      pickup_date 
    } = req.body;
    
    // Get the current cart
    let cart;
    if (req.user) {
      // Logged in user - get cart by user ID
      cart = await sequelize.models.shopping_carts.findOne({
        where: {
          user_id: req.user.id,
          is_saved: false
        }
      });
    } else if (req.session.cartId) {
      // Guest user - get cart by session ID
      cart = await sequelize.models.shopping_carts.findOne({
        where: {
          session_id: req.session.id
        }
      });
    }

    if (!cart) {
      return res.status(404).json({
        message: "Shopping cart not found",
        errorType: "NotFound",
        errorCode: "404",
        context: {
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    // Validate fulfillment method
    if (!['delivery', 'pickup'].includes(fulfillment_method)) {
      return res.status(400).json({
        message: "Invalid fulfillment method. Must be 'delivery' or 'pickup'",
        errorType: "ValidationError",
        errorCode: "400",
        context: {
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    // Validate delivery address if method is delivery
    if (fulfillment_method === 'delivery' && !delivery_address_id) {
      return res.status(400).json({
        message: "Delivery address ID is required for delivery method",
        errorType: "ValidationError",
        errorCode: "400",
        context: {
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    // Validate pickup date if method is pickup
    if (fulfillment_method === 'pickup' && !pickup_date) {
      return res.status(400).json({
        message: "Pickup date is required for pickup method",
        errorType: "ValidationError",
        errorCode: "400",
        context: {
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    // Update cart with fulfillment details
    await cart.update({
      fulfillment_method,
      delivery_address_id: fulfillment_method === 'delivery' ? delivery_address_id : null,
      pickup_date: fulfillment_method === 'pickup' ? pickup_date : null
    });

    // Get updated cart with items
    const updatedCart = await sequelize.models.shopping_carts.findByPk(cart.id, {
      include: [
        {
          model: sequelize.models.shopping_cart_items,
          as: 'items',
          include: [
            {
              model: Product,
              as: 'product',
              include: [
                {
                  model: ProductImage,
                  as: 'images',
                  where: { is_primary: true },
                  required: false,
                  limit: 1
                }
              ]
            }
          ]
        }
      ]
    });

    res.status(200).json(updatedCart);
  } catch (error) {
    console.error('Error setting cart fulfillment method:', error);
    res.status(500).json({
      message: "Failed to set cart fulfillment method",
      errorType: "ServerError",
      errorCode: "500",
      context: {
        url: req.originalUrl,
        method: req.method
      }
    });
  }
};