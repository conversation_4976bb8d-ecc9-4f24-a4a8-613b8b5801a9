import { sequelize } from '../config/database.js';
import Product from '../models/Product.js';
import ProductImage from '../models/ProductImage.js';
import { validateFileType, generateStoragePath, saveFile, deleteFile } from '../utils/fileUtils.js';
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { promisify } from 'util';
import { v4 as uuidv4 } from 'uuid';

// Configure multer for temporary storage
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const tempDir = path.join(process.cwd(), 'temp');
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }
    cb(null, tempDir);
  },
  filename: (req, file, cb) => {
    cb(null, `${Date.now()}-${file.originalname}`);
  }
});

const upload = multer({ storage });
const unlinkAsync = promisify(fs.unlink);

// Upload a product image
export const uploadProductImage = async (req, res) => {
  // Use multer to handle the file upload
  upload.single('image')(req, res, async (err) => {
    if (err) {
      return res.status(400).json({ 
        message: "Error uploading file",
        errorType: "UploadError",
        errorCode: "400",
        context: {
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    const transaction = await sequelize.transaction();

    try {
      const { productId } = req.params;
      const file = req.file;

      if (!file) {
        await transaction.rollback();
        return res.status(400).json({ 
          message: "No file uploaded",
          errorType: "ValidationError",
          errorCode: "400",
          context: {
            url: req.originalUrl,
            method: req.method
          }
        });
      }

      // Check if product exists
      const product = await Product.findByPk(productId);
      if (!product) {
        await transaction.rollback();
        return res.status(404).json({ 
          message: "Product not found",
          errorType: "NotFoundError",
          errorCode: "404",
          context: {
            url: req.originalUrl,
            method: req.method
          }
        });
      }

      // Validate file type
      const fileBuffer = fs.readFileSync(file.path);
      const fileValidation = await validateFileType(file.originalname, fileBuffer);
      
      if (!fileValidation.valid) {
        await transaction.rollback();
        await unlinkAsync(file.path);
        return res.status(400).json({ 
          message: fileValidation.reason || "Invalid file type",
          errorType: "ValidationError",
          errorCode: "400",
          context: {
            url: req.originalUrl,
            method: req.method
          }
        });
      }

      // Check if it's an image
      if (!fileValidation.detectedType.startsWith('image/')) {
        await transaction.rollback();
        await unlinkAsync(file.path);
        return res.status(400).json({ 
          message: "File must be an image",
          errorType: "ValidationError",
          errorCode: "400",
          context: {
            url: req.originalUrl,
            method: req.method
          }
        });
      }

      // Generate storage path
      const storagePath = await generateStoragePath(
        product.farm_id,
        req.user.id,
        file.originalname,
        null
      );

      // Save file to storage
      await saveFile(fileBuffer, storagePath);

      // Get the highest display order for this product
      const highestOrderImage = await ProductImage.findOne({
        where: { product_id: productId },
        order: [['display_order', 'DESC']]
      });

      const nextOrder = highestOrderImage ? highestOrderImage.display_order + 1 : 0;

      // Check if this is the first image (make it primary)
      const imageCount = await ProductImage.count({
        where: { product_id: productId }
      });

      // Create product image record
      const productImage = await ProductImage.create({
        product_id: productId,
        file_path: storagePath,
        display_order: nextOrder,
        is_primary: imageCount === 0 // First image is primary
      }, { transaction });

      await transaction.commit();
      
      // Delete temporary file
      await unlinkAsync(file.path);

      res.status(201).json(productImage);
    } catch (error) {
      await transaction.rollback();
      
      // Delete temporary file if it exists
      if (req.file) {
        try {
          await unlinkAsync(req.file.path);
        } catch (unlinkError) {
          console.error('Error deleting temporary file:', unlinkError);
        }
      }

      console.error('Error uploading product image:', error);
      res.status(500).json({ 
        message: "Failed to upload product image",
        errorType: "ServerError",
        errorCode: "500",
        context: {
          url: req.originalUrl,
          method: req.method
        }
      });
    }
  });
};

// Get all images for a product
export const getProductImages = async (req, res) => {
  try {
    const { productId } = req.params;

    // Check if product exists
    const product = await Product.findByPk(productId);
    if (!product) {
      return res.status(404).json({ 
        message: "Product not found",
        errorType: "NotFoundError",
        errorCode: "404",
        context: {
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    const images = await ProductImage.findAll({
      where: { product_id: productId },
      order: [['display_order', 'ASC']]
    });

    res.status(200).json(images);
  } catch (error) {
    console.error('Error fetching product images:', error);
    res.status(500).json({ 
      message: "Failed to fetch product images",
      errorType: "ServerError",
      errorCode: "500",
      context: {
        url: req.originalUrl,
        method: req.method
      }
    });
  }
};

// Reorder product images
export const reorderProductImages = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { productId } = req.params;
    const { imageOrder } = req.body;

    if (!Array.isArray(imageOrder)) {
      await transaction.rollback();
      return res.status(400).json({ 
        message: "imageOrder must be an array of image IDs",
        errorType: "ValidationError",
        errorCode: "400",
        context: {
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    // Check if product exists
    const product = await Product.findByPk(productId);
    if (!product) {
      await transaction.rollback();
      return res.status(404).json({ 
        message: "Product not found",
        errorType: "NotFoundError",
        errorCode: "404",
        context: {
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    // Get all images for this product
    const images = await ProductImage.findAll({
      where: { product_id: productId }
    });

    // Create a map of image IDs to images
    const imageMap = {};
    images.forEach(image => {
      imageMap[image.id] = image;
    });

    // Validate that all IDs in imageOrder exist
    for (const imageId of imageOrder) {
      if (!imageMap[imageId]) {
        await transaction.rollback();
        return res.status(400).json({ 
          message: `Image with ID ${imageId} not found`,
          errorType: "ValidationError",
          errorCode: "400",
          context: {
            url: req.originalUrl,
            method: req.method
          }
        });
      }
    }

    // Update display_order for each image
    for (let i = 0; i < imageOrder.length; i++) {
      const imageId = imageOrder[i];
      await ProductImage.update(
        { display_order: i },
        { 
          where: { id: imageId },
          transaction
        }
      );
    }

    await transaction.commit();

    // Get updated images
    const updatedImages = await ProductImage.findAll({
      where: { product_id: productId },
      order: [['display_order', 'ASC']]
    });

    res.status(200).json(updatedImages);
  } catch (error) {
    await transaction.rollback();
    console.error('Error reordering product images:', error);
    res.status(500).json({ 
      message: "Failed to reorder product images",
      errorType: "ServerError",
      errorCode: "500",
      context: {
        url: req.originalUrl,
        method: req.method
      }
    });
  }
};

// Delete a product image
export const deleteProductImage = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { productId, imageId } = req.params;

    // Check if product exists
    const product = await Product.findByPk(productId);
    if (!product) {
      await transaction.rollback();
      return res.status(404).json({ 
        message: "Product not found",
        errorType: "NotFoundError",
        errorCode: "404",
        context: {
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    // Find the image
    const image = await ProductImage.findOne({
      where: {
        id: imageId,
        product_id: productId
      }
    });

    if (!image) {
      await transaction.rollback();
      return res.status(404).json({ 
        message: "Image not found",
        errorType: "NotFoundError",
        errorCode: "404",
        context: {
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    // Delete the file from storage
    await deleteFile(image.file_path);

    // Check if this was the primary image
    const wasPrimary = image.is_primary;

    // Delete the image record
    await image.destroy({ transaction });

    // If this was the primary image, set a new primary image
    if (wasPrimary) {
      const nextPrimaryImage = await ProductImage.findOne({
        where: { product_id: productId },
        order: [['display_order', 'ASC']]
      });

      if (nextPrimaryImage) {
        await nextPrimaryImage.update(
          { is_primary: true },
          { transaction }
        );
      }
    }

    // Reorder remaining images
    const remainingImages = await ProductImage.findAll({
      where: { product_id: productId },
      order: [['display_order', 'ASC']]
    });

    for (let i = 0; i < remainingImages.length; i++) {
      await remainingImages[i].update(
        { display_order: i },
        { transaction }
      );
    }

    await transaction.commit();

    res.status(200).json({ message: "Image deleted successfully" });
  } catch (error) {
    await transaction.rollback();
    console.error('Error deleting product image:', error);
    res.status(500).json({ 
      message: "Failed to delete product image",
      errorType: "ServerError",
      errorCode: "500",
      context: {
        url: req.originalUrl,
        method: req.method
      }
    });
  }
};

// Set a product image as primary
export const setPrimaryImage = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { productId, imageId } = req.params;

    // Check if product exists
    const product = await Product.findByPk(productId);
    if (!product) {
      await transaction.rollback();
      return res.status(404).json({ 
        message: "Product not found",
        errorType: "NotFoundError",
        errorCode: "404",
        context: {
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    // Find the image
    const image = await ProductImage.findOne({
      where: {
        id: imageId,
        product_id: productId
      }
    });

    if (!image) {
      await transaction.rollback();
      return res.status(404).json({ 
        message: "Image not found",
        errorType: "NotFoundError",
        errorCode: "404",
        context: {
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    // Reset all images to not primary
    await ProductImage.update(
      { is_primary: false },
      { 
        where: { product_id: productId },
        transaction
      }
    );

    // Set this image as primary
    await image.update(
      { is_primary: true },
      { transaction }
    );

    await transaction.commit();

    // Get updated images
    const updatedImages = await ProductImage.findAll({
      where: { product_id: productId },
      order: [['display_order', 'ASC']]
    });

    res.status(200).json(updatedImages);
  } catch (error) {
    await transaction.rollback();
    console.error('Error setting primary product image:', error);
    res.status(500).json({ 
      message: "Failed to set primary product image",
      errorType: "ServerError",
      errorCode: "500",
      context: {
        url: req.originalUrl,
        method: req.method
      }
    });
  }
};