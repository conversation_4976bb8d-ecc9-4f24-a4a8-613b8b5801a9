import { Op } from 'sequelize';
import { sequelize } from '../config/database.js';
import Document from '../models/Document.js';
import DocumentFolder from '../models/DocumentFolder.js';
import User from '../models/User.js';
import UserFarm from '../models/UserFarm.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import {
  validateFileType,
  generateStoragePath,
  saveFile,
  deleteFile,
  extractTextContent,
  checkStorageQuota,
  updateStorageUsage,
  readFileContent
} from '../utils/fileUtils.js';
import FarmStorageUsage from '../models/FarmStorageUsage.js';
import Farm from '../models/Farm.js';
import SubscriptionPlan from '../models/SubscriptionPlan.js';
import {
  downloadGoogleDriveFile,
  downloadDropboxFile
} from '../utils/externalStorageUtils.js';
import { downloadFromSpaces, fileExistsInSpaces } from '../utils/spacesUtils.js';
import { uuid_nil } from '../utils/uuidUtils.js';

// Helper function to check if a user has access to a farm
const checkUserFarmAccess = async (userId, farmId) => {
  if (!userId || !farmId) return false;

  const userFarm = await UserFarm.findOne({
    where: {
      user_id: userId,
      farm_id: farmId
    }
  });

  return !!userFarm;
};

// Helper function to get folder ID or root folder ID
const getFolderIdOrRoot = async (farmId, folderId) => {
  if (folderId && folderId !== "null") {
    return folderId;
  }

  // Find the root folder for this farm
  const rootFolder = await DocumentFolder.findOne({
    where: {
      farm_id: farmId,
      parent_folder_id: uuid_nil()
    }
  });

  // If root folder exists, use its ID, otherwise use uuid_nil()
  return rootFolder ? rootFolder.id : uuid_nil();
};

// Get directory name for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Base upload directory
const uploadsDir = path.join(__dirname, '..', '..', 'uploads');

/**
 * Get all documents for a tenant
 */
export const getAllDocuments = async (req, res) => {
  try {
    const { farmId } = req.params;
    const { folderId, search, page = 1, limit = 50, sortBy = 'name', sortOrder = 'asc' } = req.query;

    // Validate farm access
    const hasAccess = await checkUserFarmAccess(req.user.id, farmId);
    if (!hasAccess && !req.user.is_global_admin) {
      return res.status(403).json({ error: 'You do not have access to this farm' });
    }

    // Build query conditions
    const where = { farm_id: farmId }; // Changed from tenant_id to farm_id

    // Filter by folder if provided
    where.folder_id = await getFolderIdOrRoot(farmId, folderId);

    // Add search condition if provided
    if (search) {
      where[Op.or] = [
        { name: { [Op.iLike]: `%${search}%` } },
        { description: { [Op.iLike]: `%${search}%` } },
        { file_type: { [Op.iLike]: `%${search}%` } }
      ];
    }

    // Calculate pagination
    const offset = (page - 1) * limit;

    // Determine sort order
    const order = [[sortBy, sortOrder.toUpperCase()]];

    // Get documents
    const { count, rows: documents } = await Document.findAndCountAll({
      where,
      include: [
        {
          model: User,
          as: 'uploader',
          attributes: ['id', 'first_name', 'last_name', 'email']
        },
        {
          model: DocumentFolder,
          as: 'documentFolder',
          attributes: ['id', 'name']
        }
      ],
      order,
      limit,
      offset
    });

    // Calculate total pages
    const totalPages = Math.ceil(count / limit);

    return res.status(200).json({
      documents,
      pagination: {
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages
      }
    });
  } catch (error) {
    console.error('Error getting documents:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Get a document by ID
 */
export const getDocumentById = async (req, res) => {
  try {
    const { id } = req.params;

    // Get document with associations
    const document = await Document.findByPk(id, {
      include: [
        {
          model: User,
          as: 'uploader',
          attributes: ['id', 'first_name', 'last_name', 'email']
        },
        {
          model: DocumentFolder,
          as: 'documentFolder',
          attributes: ['id', 'name']
        }
      ]
    });

    if (!document) {
      return res.status(404).json({ error: 'Document not found' });
    }

    // Validate farm access
    const hasAccess = await checkUserFarmAccess(req.user.id, document.farm_id);
    if (!hasAccess && !req.user.is_global_admin) {
      return res.status(403).json({ error: 'You do not have access to this document' });
    }

    return res.status(200).json(document);
  } catch (error) {
    console.error('Error getting document:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Upload a new document
 */
export const uploadDocument = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    // Get farmId from params or body
    let farmId = req.params.farmId;
    const { folderId, description, encrypt = false, farmId: bodyFarmId } = req.body;

    // If farmId is not in params, try to get it from the body
    if (!farmId && bodyFarmId) {
      farmId = bodyFarmId;
    }

    // If still no farmId, return an error
    if (!farmId) {
      await transaction.rollback();
      return res.status(400).json({ 
        message: "The requested resource could not be found",
        errorType: "NotFoundError",
        errorCode: "404",
        context: {
          url: "/documents/upload",
          method: "POST"
        }
      });
    }

    // Validate farm access
    const hasAccess = await checkUserFarmAccess(req.user.id, farmId);
    if (!hasAccess && !req.user.is_global_admin) {
      await transaction.rollback();
      return res.status(403).json({ error: 'You do not have access to this farm' });
    }

    // Check if folder exists if provided
    if (folderId) {
      const folder = await DocumentFolder.findOne({
        where: {
          id: folderId,
          farm_id: farmId // Changed from tenant_id to farm_id
        }
      });

      if (!folder) {
        await transaction.rollback();
        return res.status(404).json({ error: 'Folder not found' });
      }
    }

    // Check if file was uploaded
    if (!req.files || (!req.files.file && !req.files.files)) {
      await transaction.rollback();
      return res.status(400).json({ error: 'No file uploaded' });
    }

    // Support both 'file' and 'files' field names
    // Handle the case where 'files' is an array (multiple files uploaded)
    const file = req.files.file || (Array.isArray(req.files.files) ? req.files.files[0] : req.files.files);

    // Ensure file.data exists and is not empty
    if (!file.data || file.data.length === 0) {
      console.error('File data is empty or undefined:', {
        fileName: file.name,
        fileSize: file.size,
        hasData: !!file.data,
        dataLength: file.data ? file.data.length : 0
      });

      // If file.data is empty but file.tempFilePath exists, read from tempFilePath
      if (file.tempFilePath) {
        try {
          const fs = require('fs');
          const tempFileData = fs.readFileSync(file.tempFilePath);
          file.data = tempFileData;
          console.log('Successfully read file data from tempFilePath');
        } catch (tempFileError) {
          console.error('Error reading from tempFilePath:', tempFileError);
          await transaction.rollback();
          return res.status(400).json({ error: 'Invalid file: Could not read file data' });
        }
      } else {
        await transaction.rollback();
        return res.status(400).json({ error: 'Invalid file: File data is empty' });
      }
    }

    // Validate file type
    const fileBuffer = Buffer.from(file.data);
    console.log('File buffer created with length:', fileBuffer.length);
    const fileTypeValidation = await validateFileType(file.name, fileBuffer);

    if (!fileTypeValidation.valid) {
      await transaction.rollback();
      return res.status(400).json({ 
        error: 'Invalid file type', 
        reason: fileTypeValidation.reason 
      });
    }

    // Check storage quota
    const quotaCheck = await checkStorageQuota(farmId, file.size, req.user.is_global_admin);

    if (!quotaCheck.allowed) {
      await transaction.rollback();
      return res.status(400).json({ 
        error: quotaCheck.reason,
        currentUsage: quotaCheck.currentUsage,
        quota: quotaCheck.quota
      });
    }

    // Get the folder ID where the file will be stored
    const folderIdToUse = await getFolderIdOrRoot(farmId, folderId);

    // Generate storage path
    const storagePath = await generateStoragePath(farmId, req.user.id, file.name, folderIdToUse);

    let fullPath;
    let encryptionDetails = null;

    // Save file to disk (encrypted or not)
    if (encrypt) {
      // Save encrypted file
      const result = await saveEncryptedFile(file.tempFilePath || file.data, storagePath);
      fullPath = result.storagePath;
      encryptionDetails = result.encryptionDetails;
    } else {
      // Save unencrypted file
      fullPath = await saveFile(file.tempFilePath || file.data, storagePath);
    }

    // Extract text content for search indexing (if applicable)
    // Note: For encrypted files, we can't extract text content
    let textContent = null;
    if (!encrypt) {
      textContent = await extractTextContent(fullPath, fileTypeValidation.detectedType);
    }

    // Create document record
    const document = await Document.create({
      name: file.name,
      description: description || '',
      file_path: storagePath,
      file_size: file.size,
      file_type: path.extname(file.name).substring(1) || 'unknown',
      mime_type: fileTypeValidation.detectedType || file.mimetype,
      is_external: false,
      folder_id: await getFolderIdOrRoot(farmId, folderId),
      farm_id: farmId, // Changed from tenant_id to farm_id
      uploaded_by: req.user.id,
      is_encrypted: encrypt,
      encryption_method: encrypt ? encryptionDetails.method : null,
      encryption_key_id: encrypt ? encryptionDetails.key : null,
      encryption_iv: encrypt ? encryptionDetails.iv : null
    }, { transaction });

    // Update storage usage
    await updateStorageUsage(farmId, file.size, false, 1); // Note: function needs to be updated to use farm_id

    await transaction.commit();

    return res.status(201).json({
      message: 'Document uploaded successfully',
      document
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error uploading document:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Download a document
 */
export const downloadDocument = async (req, res) => {
  try {
    const { id } = req.params;

    // Get document
    const document = await Document.findByPk(id);

    if (!document) {
      return res.status(404).json({ error: 'Document not found' });
    }

    // Validate farm access
    const hasAccess = await checkUserFarmAccess(req.user.id, document.farm_id);
    if (!hasAccess && !req.user.is_global_admin) {
      return res.status(403).json({ error: 'You do not have access to this document' });
    }

    // Handle external documents
    if (document.is_external) {
      let fileData;

      if (document.external_source === 'google_drive') {
        fileData = await downloadGoogleDriveFile(req.user.id, document.farm_id, document.external_id); // Changed from tenant_id to farm_id
      } else if (document.external_source === 'dropbox') {
        fileData = await downloadDropboxFile(req.user.id, document.farm_id, document.file_path); // Changed from tenant_id to farm_id
      } else {
        return res.status(400).json({ error: 'Unsupported external source' });
      }

      if (!fileData.success) {
        return res.status(500).json({ error: fileData.error });
      }

      // Set headers
      res.setHeader('Content-Type', fileData.mimeType);
      res.setHeader('Content-Disposition', `attachment; filename="${document.name}"`);

      // Send file
      return res.send(fileData.buffer);
    }

    // Handle encrypted documents
    if (document.is_encrypted) {
      try {
        // Retrieve and decrypt the file
        const decryptedData = await retrieveAndDecryptFile(
          document.file_path,
          document.encryption_key_id,
          document.encryption_iv,
          document.encryption_method
        );

        // Set headers
        res.setHeader('Content-Type', document.mime_type);
        res.setHeader('Content-Disposition', `attachment; filename="${document.name}"`);

        // Send decrypted file
        return res.send(decryptedData);
      } catch (error) {
        console.error('Error decrypting document:', error);
        return res.status(500).json({ error: 'Error decrypting document' });
      }
    }

    // Handle regular (unencrypted) local documents
    try {
      // First try to get the file from Spaces
      const fileExists = await fileExistsInSpaces(document.file_path);

      if (fileExists) {
        // File exists in Spaces, download it
        const fileBuffer = await downloadFromSpaces(document.file_path);

        // Set headers
        res.setHeader('Content-Type', document.mime_type);
        res.setHeader('Content-Disposition', `attachment; filename="${document.name}"`);

        // Send file
        return res.send(fileBuffer);
      } else {
        // Fallback to local filesystem
        const filePath = path.join(uploadsDir, document.file_path);

        // Check if file exists locally
        if (!fs.existsSync(filePath)) {
          return res.status(404).json({ error: 'File not found in storage' });
        }

        // Set headers
        res.setHeader('Content-Type', document.mime_type);
        res.setHeader('Content-Disposition', `attachment; filename="${document.name}"`);

        // Stream file to response
        const fileStream = fs.createReadStream(filePath);
        fileStream.pipe(res);
      }
    } catch (error) {
      console.error('Error downloading document:', error);
      return res.status(500).json({ error: 'Error downloading document' });
    }
  } catch (error) {
    console.error('Error downloading document:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Update a document
 */
export const updateDocument = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;
    const { name, description, folderId } = req.body;

    // Get document
    const document = await Document.findByPk(id);

    if (!document) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Document not found' });
    }

    // Validate farm access
    const hasAccess = await checkUserFarmAccess(req.user.id, document.farm_id);
    if (!hasAccess && !req.user.is_global_admin) {
      await transaction.rollback();
      return res.status(403).json({ error: 'You do not have access to this document' });
    }

    // Check if folder exists if provided
    if (folderId) {
      const folder = await DocumentFolder.findOne({
        where: {
          id: folderId,
          farm_id: document.farm_id // Changed from tenant_id to farm_id
        }
      });

      if (!folder) {
        await transaction.rollback();
        return res.status(404).json({ error: 'Folder not found' });
      }
    }

    // Update document
    const updates = {};

    if (name) updates.name = name;
    if (description !== undefined) updates.description = description;
    if (folderId !== undefined) {
      updates.folder_id = await getFolderIdOrRoot(document.farm_id, folderId);
    }

    await document.update(updates, { transaction });

    await transaction.commit();

    return res.status(200).json({
      message: 'Document updated successfully',
      document
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error updating document:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Delete a document
 */
export const deleteDocument = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;

    // Get document
    const document = await Document.findByPk(id);

    if (!document) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Document not found' });
    }

    // Validate farm access
    const hasAccess = await checkUserFarmAccess(req.user.id, document.farm_id);
    if (!hasAccess && !req.user.is_global_admin) {
      await transaction.rollback();
      return res.status(403).json({ error: 'You do not have access to this document' });
    }

    // Delete file from disk if it's a local file
    if (!document.is_external) {
      await deleteFile(document.file_path);
    }

    // Update storage usage
    await updateStorageUsage(
      document.farm_id, // Changed from tenant_id to farm_id
      document.is_external ? 0 : -document.file_size,
      document.is_external,
      -1
    );

    // Delete document record
    await document.destroy({ transaction });

    await transaction.commit();

    return res.status(200).json({
      message: 'Document deleted successfully'
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error deleting document:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Get document text content for preview
 */
export const getDocumentContent = async (req, res) => {
  try {
    const { id } = req.params;

    // Get document
    const document = await Document.findByPk(id);

    if (!document) {
      return res.status(404).json({ error: 'Document not found' });
    }

    // Validate farm access
    const hasAccess = await checkUserFarmAccess(req.user.id, document.farm_id);
    if (!hasAccess && !req.user.is_global_admin) {
      return res.status(403).json({ error: 'You do not have access to this document' });
    }

    // Check if file is a text file
    const mimeType = document.mime_type || '';
    const fileExtension = document.file_type?.toLowerCase() || '';
    const isTextFile = mimeType.startsWith('text/') || 
      ['txt', 'md', 'json', 'csv', 'xml', 'html', 'css', 'js', 'ts', 'jsx', 'tsx'].includes(fileExtension);

    if (!isTextFile) {
      return res.status(400).json({ error: 'File is not a text file' });
    }

    // Handle external documents
    if (document.is_external) {
      let fileData;

      if (document.external_source === 'google_drive') {
        fileData = await downloadGoogleDriveFile(req.user.id, document.farm_id, document.external_id);
      } else if (document.external_source === 'dropbox') {
        fileData = await downloadDropboxFile(req.user.id, document.farm_id, document.file_path);
      } else {
        return res.status(400).json({ error: 'Unsupported external source' });
      }

      if (!fileData.success) {
        return res.status(500).json({ error: fileData.error });
      }

      // Convert buffer to text
      const content = fileData.buffer.toString('utf-8');
      return res.status(200).json({ content });
    }

    // Handle encrypted documents
    if (document.is_encrypted) {
      return res.status(400).json({ error: 'Cannot preview encrypted files' });
    }

    // Handle regular (unencrypted) local documents
    try {
      // First try to get the file from Spaces
      const fileExists = await fileExistsInSpaces(document.file_path);

      if (fileExists) {
        // File exists in Spaces, download it
        const fileBuffer = await downloadFromSpaces(document.file_path);
        const content = fileBuffer.toString('utf-8');
        return res.status(200).json({ content });
      } else {
        // Fallback to local filesystem
        const filePath = path.join(uploadsDir, document.file_path);

        // Check if file exists locally
        if (!fs.existsSync(filePath)) {
          return res.status(404).json({ error: 'File not found in storage' });
        }

        // Read file content
        const content = await readFileContent(filePath);
        return res.status(200).json({ content });
      }
    } catch (error) {
      console.error('Error reading document content:', error);
      return res.status(500).json({ error: 'Error reading document content' });
    }
  } catch (error) {
    console.error('Error getting document content:', error);
    return res.status(500).json({ error: error.message });
  }
};

export const searchDocuments = async (req, res) => {
  try {
    const { farmId } = req.params;
    const { 
      query, 
      fileType, 
      startDate, 
      endDate, 
      folderId,
      page = 1, 
      limit = 50 
    } = req.query;

    // Validate farm access
    const hasAccess = await checkUserFarmAccess(req.user.id, farmId);
    if (!hasAccess && !req.user.is_global_admin) {
      return res.status(403).json({ error: 'You do not have access to this farm' });
    }

    // Build query conditions
    const where = { farm_id: farmId }; // Changed from tenant_id to farm_id

    // Add search query if provided
    if (query) {
      where[Op.or] = [
        { name: { [Op.iLike]: `%${query}%` } },
        { description: { [Op.iLike]: `%${query}%` } }
      ];
    }

    // Filter by file type if provided
    if (fileType) {
      where.file_type = fileType;
    }

    // Filter by date range if provided
    if (startDate || endDate) {
      where.created_at = {};

      if (startDate) {
        where.created_at[Op.gte] = new Date(startDate);
      }

      if (endDate) {
        where.created_at[Op.lte] = new Date(endDate);
      }
    }

    // Filter by folder if provided
    where.folder_id = await getFolderIdOrRoot(farmId, folderId);

    // Calculate pagination
    const offset = (page - 1) * limit;

    // Get documents
    const { count, rows: documents } = await Document.findAndCountAll({
      where,
      include: [
        {
          model: User,
          as: 'uploader',
          attributes: ['id', 'first_name', 'last_name', 'email']
        },
        {
          model: DocumentFolder,
          as: 'documentFolder',
          attributes: ['id', 'name']
        }
      ],
      order: [['created_at', 'DESC']],
      limit,
      offset
    });

    // Calculate total pages
    const totalPages = Math.ceil(count / limit);

    return res.status(200).json({
      documents,
      pagination: {
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages
      }
    });
  } catch (error) {
    console.error('Error searching documents:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Get storage quota information for a farm
 */
export const getStorageQuotaInfo = async (req, res) => {
  try {
    const { farmId } = req.params;

    // Validate farm access
    const hasAccess = await checkUserFarmAccess(req.user.id, farmId);
    if (!hasAccess && !req.user.is_global_admin) {
      return res.status(403).json({ error: 'You do not have access to this farm' });
    }

    // Check if user is a global admin - they get unlimited storage
    if (req.user.is_global_admin) {
      // For global admins, we still need to get the document count and usage
      let storageUsage = await FarmStorageUsage.findOne({
        where: { farm_id: farmId }
      });

      if (!storageUsage) {
        // Create storage usage record if it doesn't exist
        storageUsage = await FarmStorageUsage.create({
          farm_id: farmId,
          total_bytes_used: 0,
          document_count: 0,
          external_document_count: 0,
          last_calculated_at: new Date()
        });
      }

      // Return unlimited quota for global admins
      const quotaInfo = {
        currentUsage: storageUsage.total_bytes_used,
        quota: Infinity, // Unlimited quota
        usagePercentage: 0, // Always 0% since quota is unlimited
        documentCount: storageUsage.document_count,
        externalDocumentCount: storageUsage.external_document_count,
        quotaGB: "Unlimited", // Show "Unlimited" instead of a number
        usageGB: Math.round((storageUsage.total_bytes_used / (1024 * 1024 * 1024)) * 100) / 100,
        lastCalculatedAt: storageUsage.last_calculated_at,
        isGlobalAdmin: true
      };

      return res.status(200).json(quotaInfo);
    }

    // Get farm's subscription plan
    const farm = await Farm.findByPk(farmId, {
      include: [{ model: SubscriptionPlan }]
    });

    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    if (!farm.SubscriptionPlan) {
      return res.status(404).json({ error: 'Farm has no active subscription plan' });
    }

    // Get storage quota from subscription plan (convert GB to bytes)
    const storageQuotaBytes = farm.SubscriptionPlan.storage_quota_gb * 1024 * 1024 * 1024;

    // Get current storage usage
    let storageUsage = await FarmStorageUsage.findOne({
      where: { farm_id: farmId }
    });

    if (!storageUsage) {
      // Create storage usage record if it doesn't exist
      storageUsage = await FarmStorageUsage.create({
        farm_id: farmId,
        total_bytes_used: 0,
        document_count: 0,
        external_document_count: 0,
        last_calculated_at: new Date()
      });
    }

    // Format the response
    const quotaInfo = {
      currentUsage: storageUsage.total_bytes_used,
      quota: storageQuotaBytes,
      usagePercentage: Math.round((storageUsage.total_bytes_used / storageQuotaBytes) * 100),
      documentCount: storageUsage.document_count,
      externalDocumentCount: storageUsage.external_document_count,
      quotaGB: farm.SubscriptionPlan.storage_quota_gb,
      usageGB: Math.round((storageUsage.total_bytes_used / (1024 * 1024 * 1024)) * 100) / 100, // Convert to GB with 2 decimal places
      lastCalculatedAt: storageUsage.last_calculated_at
    };

    return res.status(200).json(quotaInfo);
  } catch (error) {
    console.error('Error getting storage quota info:', error);
    return res.status(500).json({ error: error.message });
  }
};
