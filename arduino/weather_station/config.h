/*
 * NxtAcre Weather Station - Configuration
 * 
 * This file contains configuration settings for the NxtAcre Weather Station.
 */

#ifndef CONFIG_H
#define CONFIG_H

// WiFi Configuration
#define WIFI_SSID "YourWiFiSSID"         // WiFi network name
#define WIFI_PASSWORD "YourWiFiPassword"  // WiFi password
#define WIFI_RETRY_DELAY 5000            // Delay between WiFi connection attempts (ms)
#define WIFI_MAX_RETRIES 10              // Maximum number of WiFi connection attempts

// Server Configuration
#define SERVER_URL "http://your-nxtacre-server.com/api/iot/data/external"  // Server URL for sending data
#define API_KEY "your-api-key"           // API key for authentication
#define DEVICE_IDENTIFIER "weather_station_1"  // Unique identifier for this device

// Timing Configuration
#define SENSOR_READ_INTERVAL 60000       // Interval for reading sensors (ms) - 1 minute
#define DATA_SEND_INTERVAL 300000        // Interval for sending data to server (ms) - 5 minutes

// Pin Configuration
// BME280 uses I2C, so no specific pins needed beyond SDA and SCL
#define SDA_PIN 21                       // I2C SDA pin
#define SCL_PIN 22                       // I2C SCL pin
#define RAIN_GAUGE_PIN 4                 // Digital pin for rain gauge sensor
#define ANEMOMETER_PIN 5                 // Digital pin for anemometer
#define WIND_VANE_PIN 34                 // Analog pin for wind vane
#define BATTERY_LEVEL_PIN 35             // Analog pin for battery level monitoring
#define SOLAR_VOLTAGE_PIN 36             // Analog pin for solar panel voltage monitoring

// Sensor Calibration
#define RAIN_GAUGE_FACTOR 0.2794         // Rain gauge factor (mm per tip)
#define ANEMOMETER_FACTOR 1.492          // Anemometer factor (m/s per Hz)
#define BATTERY_MAX_VOLTAGE 4.2          // Maximum battery voltage (V)
#define BATTERY_MIN_VOLTAGE 3.3          // Minimum battery voltage (V)

// Debug Configuration
#define DEBUG_ENABLED true               // Enable debug output

#endif // CONFIG_H